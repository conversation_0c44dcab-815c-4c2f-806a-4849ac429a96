from __future__ import annotations

from dataclasses import dataclass, field, asdict
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Any
import json
from pathlib import Path


class RunPhase(str, Enum):
    queued = "queued"
    running = "running"
    succeeded = "succeeded"
    failed = "failed"
    cancelled = "cancelled"


TERMINAL_STATES = {RunPhase.succeeded, RunPhase.failed, RunPhase.cancelled}


def utc_now_iso() -> str:
    return datetime.now(timezone.utc).isoformat()


@dataclass
class Transition:
    at: str
    from_state: Optional[str]
    to_state: str
    note: Optional[str] = None


@dataclass
class RunStatus:
    run_id: str
    state: RunPhase = RunPhase.queued
    created_at: str = field(default_factory=utc_now_iso)
    updated_at: str = field(default_factory=utc_now_iso)
    started_at: Optional[str] = None
    ended_at: Optional[str] = None
    duration_ms: Optional[int] = None
    region_used: Optional[str] = None
    fallback_applied: bool = False
    error: Optional[str] = None
    metadata_status_warning: Optional[str] = None
    history: List[Transition] = field(default_factory=list)
    # Optimistic concurrency/versioning and cancellation audit
    version: int = 0
    cancelled_at: Optional[str] = None
    cancellation_reason: Optional[str] = None
    cancelled_by: Optional[str] = None

    def can_transition(self, to_state: RunPhase) -> bool:
        if self.state in TERMINAL_STATES:
            return False
        if self.state == RunPhase.queued and to_state in {RunPhase.running, RunPhase.cancelled}:
            return True
        if self.state == RunPhase.running and to_state in {RunPhase.succeeded, RunPhase.failed, RunPhase.cancelled}:
            return True
        return False

    def transition(self, to_state: RunPhase, note: Optional[str] = None) -> None:
        if not self.can_transition(to_state):
            raise ValueError(f"invalid_transition:{self.state}->{to_state}")
        prev = self.state
        now = utc_now_iso()
        self.state = to_state
        self.updated_at = now
        self.version += 1
        self.history.append(Transition(at=now, from_state=prev, to_state=to_state, note=note))
        if prev == RunPhase.queued and to_state == RunPhase.running:
            self.started_at = now
        if to_state == RunPhase.cancelled:
            self.cancelled_at = now
        if to_state in TERMINAL_STATES:
            self.ended_at = now
            if self.started_at:
                try:
                    started = datetime.fromisoformat(self.started_at)
                    ended = datetime.fromisoformat(now)
                    self.duration_ms = int((ended - started).total_seconds() * 1000)
                except Exception:
                    self.duration_ms = None

    def cancel(self, reason: str = "user_request", cancelled_by: str = "user") -> None:
        """Force a terminal cancellation if allowed."""
        if self.state in TERMINAL_STATES:
            raise ValueError("invalid_transition:already_terminal")
        if not self.can_transition(RunPhase.cancelled):
            raise ValueError(f"invalid_transition:{self.state}->cancelled")
        self.cancellation_reason = reason
        self.cancelled_by = cancelled_by
        self.transition(RunPhase.cancelled, note=f"cancelled:{reason}:{cancelled_by}")

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data["state"] = self.state.value
        data["history"] = [asdict(h) for h in self.history]
        return data

    @classmethod
    def from_file(cls, path: Path) -> "RunStatus":
        data = json.loads(path.read_text(encoding="utf-8"))
        state = RunPhase(data.get("state", "queued"))
        history = [
            Transition(
                at=h.get("at"),
                from_state=h.get("from_state"),
                to_state=h.get("to_state"),
                note=h.get("note"),
            )
            for h in data.get("history", [])
        ]
        return cls(
            run_id=data["run_id"],
            state=state,
            created_at=data.get("created_at") or utc_now_iso(),
            updated_at=data.get("updated_at") or utc_now_iso(),
            started_at=data.get("started_at"),
            ended_at=data.get("ended_at"),
            duration_ms=data.get("duration_ms"),
            region_used=data.get("region_used"),
            fallback_applied=bool(data.get("fallback_applied", False)),
            error=data.get("error"),
            metadata_status_warning=data.get("metadata_status_warning"),
            history=history,
            version=int(data.get("version", 0)),
            cancelled_at=data.get("cancelled_at"),
            cancellation_reason=data.get("cancellation_reason"),
            cancelled_by=data.get("cancelled_by"),
        )

    def save(self, path: Path) -> None:
        path.write_text(json.dumps(self.to_dict(), indent=2), encoding="utf-8")