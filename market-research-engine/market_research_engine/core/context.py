from __future__ import annotations

import hashlib
import json
import os
import platform
import subprocess
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional, Tuple

try:
    import pkg_resources
    PKG_RESOURCES_AVAILABLE = True
except ImportError:
    try:
        import importlib.metadata as metadata
        PKG_RESOURCES_AVAILABLE = True
    except ImportError:
        PKG_RESOURCES_AVAILABLE = False


def _utc() -> str:
    return datetime.now(timezone.utc).isoformat()


def _safe_getenv(name: str) -> Optional[str]:
    try:
        return os.getenv(name)
    except Exception:
        return None


def _get_key_package_versions() -> Dict[str, str]:
    """Get versions of key packages used by the application."""
    key_packages = [
        "fastapi", "uvicorn", "pydantic", "httpx", "tenacity", 
        "youtube-transcript-api", "openai", "crewai", "structlog", "loguru"
    ]
    
    versions = {}
    if not PKG_RESOURCES_AVAILABLE:
        return versions
    
    for pkg_name in key_packages:
        try:
            if 'pkg_resources' in sys.modules:
                version = pkg_resources.get_distribution(pkg_name).version
            else:
                # Use importlib.metadata
                version = metadata.version(pkg_name)
            versions[pkg_name] = version
        except Exception:
            # Package not installed or version not available
            continue
    
    return versions


def _git_info(project_root: Path) -> Dict[str, Optional[str]]:
    def _try(cmd: list[str]) -> Optional[str]:
        try:
            out = subprocess.check_output(cmd, cwd=str(project_root), stderr=subprocess.DEVNULL, timeout=1.5)
            return out.decode("utf-8", errors="ignore").strip()
        except Exception:
            return None

    return {
        "commit_sha": _try(["git", "rev-parse", "HEAD"]),
        "branch": _try(["git", "rev-parse", "--abbrev-ref", "HEAD"]),
        "tag": _try(["git", "describe", "--tags", "--exact-match"]),
        "build_timestamp": _utc(),
        "build_id": _safe_getenv("BUILD_ID"),
        "deployment_env": _safe_getenv("APP_ENV") or "development",
    }


def _redact_value(val: Any) -> Any:
    try:
        if val is None:
            return None
        if isinstance(val, bool):
            return val
        if isinstance(val, (int, float)):
            return val
        if isinstance(val, str):
            s = val
            # Preserve last 4 chars where appropriate
            last4 = s[-4:] if len(s) >= 4 else s
            sha = hashlib.sha256(s.encode("utf-8")).hexdigest()[:8]
            return f"***REDACTED***::{last4}::{sha}"
        if isinstance(val, (list, tuple)):
            return [_redact_value(v) for v in val]
        if isinstance(val, dict):
            return {k: _redact_value(v) for k, v in val.items()}
        return "***REDACTED***"
    except Exception:
        return "***REDACTED***"


def build_context_v2(
    *,
    project_root: Path,
    app_version: str,
    idempotency_key: Optional[str],
    trigger_type: str = "api",
    parent_run_id: Optional[str] = None,
    request_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    redacted_config_source: Optional[Dict[str, Any]] = None,
    region: Optional[str] = None,
    zone: Optional[str] = None,
    feature_flags: Optional[Dict[str, bool]] = None,
) -> Dict[str, Any]:
    """
    Construct a versioned context.json document with redaction.

    Schema version: 2
    """
    exec_env = {
        "os": platform.system(),
        "os_version": platform.version(),
        "machine": platform.machine(),
        "arch": platform.architecture()[0],
        "python_version": platform.python_version(),
        "python_executable": sys.executable,
        "package_versions": _get_key_package_versions(),
        "container_image": _safe_getenv("CONTAINER_IMAGE"),
        "container_tag": _safe_getenv("CONTAINER_TAG"),
        "region": region or _safe_getenv("REGION") or "unknown",
        "zone": zone or _safe_getenv("ZONE") or None,
        "hardware_class": _safe_getenv("HARDWARE_CLASS") or "standard",
        "feature_flags": feature_flags or {},
    }

    # Redact provided config structure if any
    redacted_cfg: Dict[str, Any] = {}
    if redacted_config_source:
        try:
            redacted_cfg = _redact_value(redacted_config_source)  # type: ignore[assignment]
        except Exception:
            redacted_cfg = {"error": "redaction_failed"}

    invocation = {
        "request_id": request_id or _safe_getenv("REQUEST_ID"),
        "correlation_id": correlation_id or _safe_getenv("CORRELATION_ID"),
        "idempotency_key": idempotency_key,
        "triggered_by": trigger_type,
        "parent_run_id": parent_run_id,
    }

    code_ver = _git_info(project_root)

    return {
        "schema_version": 2,
        "created_at": _utc(),
        "versions": {
            "app_version": app_version,
        },
        "execution_environment": exec_env,
        "redacted_config": redacted_cfg,
        "invocation_metadata": invocation,
        "code_version": code_ver,
    }