from __future__ import annotations

import asyncio
import time
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Any, Awaitable, Callable, Dict, List, Optional, Protocol, Tuple, Union

JsonDict = Dict[str, Any]


def utc_iso() -> str:
    return datetime.now(timezone.utc).isoformat()


@dataclass
class StepResult:
    step_id: str
    started_at: str
    ended_at: str
    duration_ms: int
    attempts: int
    timed_out: bool
    cancelled: bool
    succeeded: bool
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    output_summary: Optional[JsonDict] = None
    metadata: JsonDict = field(default_factory=dict)


@dataclass
class ExecutionContext:
    run_id: str
    correlation_id: Optional[str] = None
    request_id: Optional[str] = None
    cancel_event: Optional[asyncio.Event] = None  # used by async paths
    # For sync code supporting cancellation, use a callable to check cancellation
    is_cancelled: Optional[Callable[[], bool]] = None
    # Tracing hooks (no-op defaults)
    on_event: Optional[Callable[[str, JsonDict], None]] = None


class Step(Protocol):
    """
    A unit of work that can run under the executor.
    Implementations should be stateless or use provided metadata/context for state.
    """

    step_id: str

    def prepare(self, ctx: ExecutionContext, inputs: JsonDict) -> JsonDict:  # inputs.json
        ...

    def execute(self, ctx: ExecutionContext, prepared: JsonDict) -> JsonDict:  # outputs.json
        ...

    def finalize(self, ctx: ExecutionContext, outputs: JsonDict) -> None:
        ...


class AsyncStep(Protocol):
    """
    Async variant of Step. Supports mixed environments.
    """

    step_id: str

    async def prepare(self, ctx: ExecutionContext, inputs: JsonDict) -> JsonDict:
        ...

    async def execute(self, ctx: ExecutionContext, prepared: JsonDict) -> JsonDict:
        ...

    async def finalize(self, ctx: ExecutionContext, outputs: JsonDict) -> None:
        ...


@dataclass
class RetryPolicy:
    max_attempts: int = 1
    backoff_base_sec: float = 0.5
    backoff_factor: float = 2.0
    max_backoff_sec: float = 8.0

    def compute_backoff(self, attempt: int) -> float:
        if attempt <= 1:
            return 0.0
        delay = self.backoff_base_sec * (self.backoff_factor ** (attempt - 2))
        return min(self.max_backoff_sec, delay)


@dataclass
class TimeoutPolicy:
    step_timeout_sec: Optional[float] = None  # None = no timeout


@dataclass
class ExecutionPolicy:
    retry: RetryPolicy = field(default_factory=RetryPolicy)
    timeout: TimeoutPolicy = field(default_factory=TimeoutPolicy)


class Executor(Protocol):
    """
    Executor coordinates prepare -> execute -> finalize for Steps.
    """

    def run_step(self, ctx: ExecutionContext, step: Step, inputs: JsonDict, policy: ExecutionPolicy) -> StepResult:
        ...

    async def run_step_async(self, ctx: ExecutionContext, step: Union[Step, AsyncStep], inputs: JsonDict, policy: ExecutionPolicy) -> StepResult:
        ...


class DefaultExecutor:
    """
    Reference executor implementation with retry, timeout, and cancellation handling.
    Emits lifecycle events through ctx.on_event hook for observability.
    """

    def _emit(self, ctx: ExecutionContext, event: str, **fields: Any) -> None:
        if ctx.on_event:
            try:
                ctx.on_event(event, {"ts": utc_iso(), **fields})
            except Exception:
                # Never fail execution on logging errors
                pass

    def _cancelled(self, ctx: ExecutionContext) -> bool:
        try:
            if ctx.cancel_event and ctx.cancel_event.is_set():
                return True
            if ctx.is_cancelled and ctx.is_cancelled():
                return True
        except Exception:
            return False
        return False

    def run_step(self, ctx: ExecutionContext, step: Step, inputs: JsonDict, policy: ExecutionPolicy) -> StepResult:
        start_ts = time.time()
        started_at = utc_iso()
        attempts = 0
        timed_out = False
        cancelled = False
        output_summary: Optional[JsonDict] = None
        error_type: Optional[str] = None
        error_message: Optional[str] = None

        self._emit(ctx, "step_start", step_id=step.step_id, mode="sync", attempts=policy.retry.max_attempts)

        prepared: JsonDict = {}
        try:
            prepared = step.prepare(ctx, inputs or {})
        except Exception as e:
            error_type = type(e).__name__
            error_message = str(e)
            self._emit(ctx, "step_prepare_failed", step_id=step.step_id, error=error_type, message=error_message)
            ended_at = utc_iso()
            duration_ms = int((time.time() - start_ts) * 1000)
            return StepResult(
                step_id=step.step_id,
                started_at=started_at,
                ended_at=ended_at,
                duration_ms=duration_ms,
                attempts=1,
                timed_out=False,
                cancelled=False,
                succeeded=False,
                error_type=error_type,
                error_message=error_message,
                output_summary=None,
            )

        while True:
            if self._cancelled(ctx):
                cancelled = True
                self._emit(ctx, "step_cancelled_before_execute", step_id=step.step_id)
                break

            attempts += 1
            try:
                # Handle timeout via alarmed execution wrapper
                if policy.timeout.step_timeout_sec and policy.timeout.step_timeout_sec > 0:
                    # Basic timeout using time checks inside callable
                    # For sync functions, we simulate by checking elapsed time between calls.
                    t0 = time.time()
                    output = step.execute(ctx, prepared)
                    elapsed = time.time() - t0
                    if elapsed > policy.timeout.step_timeout_sec:
                        raise TimeoutError("step_timeout_exceeded")
                else:
                    output = step.execute(ctx, prepared)

                output_summary = output if isinstance(output, dict) else {"result": str(output)}
                succeeded = True
                self._emit(ctx, "step_execute_succeeded", step_id=step.step_id, attempts=attempts)
                try:
                    step.finalize(ctx, output_summary or {})
                except Exception as e_fin:
                    # Finalize should not fail execution result, but we capture for diagnostics
                    self._emit(ctx, "step_finalize_error", step_id=step.step_id, error=type(e_fin).__name__, message=str(e_fin))
                ended_at = utc_iso()
                duration_ms = int((time.time() - start_ts) * 1000)
                return StepResult(
                    step_id=step.step_id,
                    started_at=started_at,
                    ended_at=ended_at,
                    duration_ms=duration_ms,
                    attempts=attempts,
                    timed_out=False,
                    cancelled=False,
                    succeeded=succeeded,
                    output_summary=output_summary,
                )
            except TimeoutError as te:
                timed_out = True
                error_type = type(te).__name__
                error_message = str(te)
                self._emit(ctx, "step_timeout", step_id=step.step_id, attempts=attempts, timeout_sec=policy.timeout.step_timeout_sec)
                break
            except Exception as e:
                error_type = type(e).__name__
                error_message = str(e)
                self._emit(ctx, "step_execute_failed", step_id=step.step_id, attempts=attempts, error=error_type, message=error_message)
                if attempts >= max(1, policy.retry.max_attempts):
                    break
                # backoff
                delay = policy.retry.compute_backoff(attempts + 1)
                if delay > 0:
                    self._emit(ctx, "step_retry_backoff", step_id=step.step_id, delay_sec=delay)
                    # honour cancellation during backoff
                    t_end = time.time() + delay
                    while time.time() < t_end:
                        if self._cancelled(ctx):
                            cancelled = True
                            break
                        time.sleep(min(0.05, t_end - time.time()))
                    if cancelled:
                        break

        # failed / timed out / cancelled path
        ended_at = utc_iso()
        duration_ms = int((time.time() - start_ts) * 1000)
        return StepResult(
            step_id=step.step_id,
            started_at=started_at,
            ended_at=ended_at,
            duration_ms=duration_ms,
            attempts=attempts if attempts > 0 else 1,
            timed_out=timed_out,
            cancelled=cancelled,
            succeeded=False,
            error_type=error_type,
            error_message=error_message,
            output_summary=output_summary,
        )

    async def run_step_async(self, ctx: ExecutionContext, step: Union[Step, AsyncStep], inputs: JsonDict, policy: ExecutionPolicy) -> StepResult:
        start_ts = time.time()
        started_at = utc_iso()
        attempts = 0
        timed_out = False
        cancelled = False
        output_summary: Optional[JsonDict] = None
        error_type: Optional[str] = None
        error_message: Optional[str] = None

        self._emit(ctx, "step_start", step_id=step.step_id, mode="async", attempts=policy.retry.max_attempts)

        # prepare
        try:
            if hasattr(step, "prepare") and asyncio.iscoroutinefunction(getattr(step, "prepare")):
                prepared = await getattr(step, "prepare")(ctx, inputs or {})  # type: ignore[misc]
            else:
                prepared = step.prepare(ctx, inputs or {})  # type: ignore[assignment]
        except Exception as e:
            error_type = type(e).__name__
            error_message = str(e)
            self._emit(ctx, "step_prepare_failed", step_id=step.step_id, error=error_type, message=error_message)
            ended_at = utc_iso()
            duration_ms = int((time.time() - start_ts) * 1000)
            return StepResult(
                step_id=step.step_id,
                started_at=started_at,
                ended_at=ended_at,
                duration_ms=duration_ms,
                attempts=1,
                timed_out=False,
                cancelled=False,
                succeeded=False,
                error_type=error_type,
                error_message=error_message,
                output_summary=None,
            )

        while True:
            if self._cancelled(ctx):
                cancelled = True
                self._emit(ctx, "step_cancelled_before_execute", step_id=step.step_id)
                break

            attempts += 1
            try:
                # timeout
                if policy.timeout.step_timeout_sec and policy.timeout.step_timeout_sec > 0:
                    async def _exec_with_timeout() -> JsonDict:
                        if hasattr(step, "execute") and asyncio.iscoroutinefunction(getattr(step, "execute")):
                            return await getattr(step, "execute")(ctx, prepared)  # type: ignore[misc]
                        else:
                            # offload sync to thread
                            loop = asyncio.get_running_loop()
                            return await loop.run_in_executor(None, step.execute, ctx, prepared)  # type: ignore[arg-type]
                    output = await asyncio.wait_for(_exec_with_timeout(), timeout=policy.timeout.step_timeout_sec)
                else:
                    if hasattr(step, "execute") and asyncio.iscoroutinefunction(getattr(step, "execute")):
                        output = await getattr(step, "execute")(ctx, prepared)  # type: ignore[misc]
                    else:
                        loop = asyncio.get_running_loop()
                        output = await loop.run_in_executor(None, step.execute, ctx, prepared)  # type: ignore[arg-type]

                output_summary = output if isinstance(output, dict) else {"result": str(output)}
                self._emit(ctx, "step_execute_succeeded", step_id=step.step_id, attempts=attempts)
                try:
                    if hasattr(step, "finalize") and asyncio.iscoroutinefunction(getattr(step, "finalize")):
                        await getattr(step, "finalize")(ctx, output_summary or {})  # type: ignore[misc]
                    else:
                        step.finalize(ctx, output_summary or {})  # type: ignore[arg-type]
                except Exception as e_fin:
                    self._emit(ctx, "step_finalize_error", step_id=step.step_id, error=type(e_fin).__name__, message=str(e_fin))
                ended_at = utc_iso()
                duration_ms = int((time.time() - start_ts) * 1000)
                return StepResult(
                    step_id=step.step_id,
                    started_at=started_at,
                    ended_at=ended_at,
                    duration_ms=duration_ms,
                    attempts=attempts,
                    timed_out=False,
                    cancelled=False,
                    succeeded=True,
                    output_summary=output_summary,
                )
            except asyncio.TimeoutError as te:
                timed_out = True
                error_type = type(te).__name__
                error_message = "step_timeout_exceeded"
                self._emit(ctx, "step_timeout", step_id=step.step_id, attempts=attempts, timeout_sec=policy.timeout.step_timeout_sec)
                break
            except Exception as e:
                error_type = type(e).__name__
                error_message = str(e)
                self._emit(ctx, "step_execute_failed", step_id=step.step_id, attempts=attempts, error=error_type, message=error_message)
                if attempts >= max(1, policy.retry.max_attempts):
                    break
                delay = policy.retry.compute_backoff(attempts + 1)
                if delay > 0:
                    self._emit(ctx, "step_retry_backoff", step_id=step.step_id, delay_sec=delay)
                    try:
                        await asyncio.wait_for(asyncio.sleep(delay), timeout=delay + 0.1)
                    except asyncio.CancelledError:
                        cancelled = True
                        break

        ended_at = utc_iso()
        duration_ms = int((time.time() - start_ts) * 1000)
        return StepResult(
            step_id=step.step_id,
            started_at=started_at,
            ended_at=ended_at,
            duration_ms=duration_ms,
            attempts=attempts if attempts > 0 else 1,
            timed_out=timed_out,
            cancelled=cancelled,
            succeeded=False,
            error_type=error_type,
            error_message=error_message,
            output_summary=output_summary,
        )


# Minimal mock implementations for tests and E2E scaffolding

@dataclass
class MockStep:
    step_id: str = "mock"
    fail_times: int = 0
    sleep_sec: float = 0.0
    prepared_payload: Optional[JsonDict] = None
    executed_payload: Optional[JsonDict] = None

    def prepare(self, ctx: ExecutionContext, inputs: JsonDict) -> JsonDict:
        self.prepared_payload = {"inputs": inputs}
        return {"prepared": True, **inputs}

    def execute(self, ctx: ExecutionContext, prepared: JsonDict) -> JsonDict:
        if self.sleep_sec > 0:
            t_end = time.time() + self.sleep_sec
            while time.time() < t_end:
                # cooperative cancellation check
                if ctx.is_cancelled and ctx.is_cancelled():
                    raise RuntimeError("cancelled_during_sleep")
                time.sleep(min(0.02, t_end - time.time()))
        if self.fail_times > 0:
            self.fail_times -= 1
            raise RuntimeError("intentional_failure")
        self.executed_payload = {"prepared": prepared}
        return {"ok": True, "prepared": prepared}

    def finalize(self, ctx: ExecutionContext, outputs: JsonDict) -> None:
        # no-op
        return


class MockExecutor(DefaultExecutor):
    """
    Exposes DefaultExecutor behavior for tests but as a named type.
    """
    pass