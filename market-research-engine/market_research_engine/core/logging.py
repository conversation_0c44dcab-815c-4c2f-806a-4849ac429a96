from __future__ import annotations

import json
import os
from dataclasses import asdict, dataclass
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional


def utc_iso() -> str:
    return datetime.now(timezone.utc).isoformat()


@dataclass
class LogLine:
    ts: str
    level: str
    run_id: Optional[str]
    phase: Optional[str]
    step: Optional[str]
    event: str
    fields: Dict[str, Any]

    def to_json(self) -> str:
        payload = {
            "ts": self.ts,
            "level": self.level,
            "run_id": self.run_id,
            "phase": self.phase,
            "step": self.step,
            "event": self.event,
            "fields": self.fields or {},
        }
        return json.dumps(payload, separators=(",", ":"), ensure_ascii=False)


class NdjsonLogger:
    """
    Simple, process-safe NDJSON logger with size-based rotation.

    - Appends one JSON object per line.
    - Rotates when the file exceeds max_bytes (renames to .1, overwriting existing .1).
    - Best-effort write; logging must never raise.
    """

    def __init__(self, log_path: Path, max_bytes: int = 2_000_000) -> None:
        self.log_path = log_path
        self.max_bytes = max(32_000, int(max_bytes))
        self.log_path.parent.mkdir(parents=True, exist_ok=True)

    def _rotate_if_needed(self) -> None:
        try:
            if self.log_path.exists() and self.log_path.stat().st_size > self.max_bytes:
                rotated = self.log_path.with_suffix(self.log_path.suffix + ".1")
                try:
                    if rotated.exists():
                        rotated.unlink(missing_ok=True)  # type: ignore[arg-type]
                except Exception:
                    pass
                try:
                    self.log_path.rename(rotated)
                except Exception:
                    # If rename fails, skip rotation
                    pass
        except Exception:
            # never raise from logging
            pass

    def log(self, level: str, event: str, run_id: Optional[str] = None, phase: Optional[str] = None, step: Optional[str] = None, **fields: Any) -> None:
        try:
            self._rotate_if_needed()
            line = LogLine(
                ts=utc_iso(),
                level=str(level).upper(),
                run_id=run_id,
                phase=phase,
                step=step,
                event=event,
                fields=fields or {},
            ).to_json()
            with self.log_path.open("a", encoding="utf-8") as fh:
                fh.write(line + "\n")
        except Exception:
            # never raise from logging
            pass


def configure_logging() -> None:
    """
    Placeholder for global logging configuration.
    Currently intentionally minimal since we rely on NdjsonLogger for run-scoped logs.
    """
    # In future, wire structlog/loguru here if needed.
    return