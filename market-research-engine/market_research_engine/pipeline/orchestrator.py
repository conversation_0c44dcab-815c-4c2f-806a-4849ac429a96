from __future__ import annotations

import json
import os
from pathlib import Path
from typing import Optional

from ..core.state import RunStatus, RunPhase  # type: ignore
from ..utils.atomic import atomic_write_json  # type: ignore
from .agents.recon import make_recon_agent
from .agents.vision import make_vision_agent
from .agents.synthesis import make_synthesis_agent

# Query tracking integration
try:
    from ..services.query_tracker import QueryTracker
except ImportError:
    QueryTracker = None

# Dashboard formatting
try:
    from ..services.dashboard_formatter import DashboardFormatter
except ImportError:
    DashboardFormatter = None


class Orchestrator:
    """
    Minimal synchronous orchestrator for MVP live path:
      1) ReconAgent → writes artifacts/recon.json
      2) VisionAgent → writes artifacts/vision.json (analyzes thumbnails for face detection)
      3) SynthesisAgent → writes artifacts/synthesis.json + artifacts/synthesis.md (concise)

    Behavior:
      - Honors LIVE_PROVIDERS env flag; defaults false (fakes).
      - Updates status.json version via RunStatus.transition at terminal boundaries.
      - Best-effort cancellation support can be added by checking a flag between steps.
    """

    def __init__(self) -> None:
        # LIVE_PROVIDERS=false by default for safety in CI/local
        self.live = (os.getenv("LIVE_PROVIDERS", "false").lower() == "true")
        
        # Initialize query tracker for monitoring queries over time
        self.query_tracker = None
        if QueryTracker:
            try:
                # Store query history in runs directory
                tracking_dir = Path.cwd() / "runs" / "query_tracking"
                self.query_tracker = QueryTracker(tracking_dir)
            except Exception:
                pass  # Soft fail if tracking unavailable

    # Public sync entry for existing POST /runs call site
    def run_sync(self, *, run_dir: Path, query: str, region: str, depth: str = "full") -> None:
        """
        Execute Recon → Vision → Synthesis. Depth can be "lite" or "full"; both run the same MVP path for now.
        """
        run_dir = Path(run_dir)
        status_fp = run_dir / "status.json"
        status = RunStatus.from_file(status_fp)

        # Step 1: Recon
        recon_agent = make_recon_agent(runs_dir=run_dir.parent)
        
        # Use asyncio directly with proper event loop handling
        import asyncio
        import concurrent.futures
        
        # Execute recon in a separate thread to avoid event loop conflicts when run from FastAPI
        try:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                def run_recon_in_thread():
                    asyncio.run(self._run_recon(recon_agent, run_dir, query, region))
                future = executor.submit(run_recon_in_thread)
                future.result()  # Wait for completion
        except Exception as e:
            raise RuntimeError(f"Recon step failed: {str(e)}") from e

        # Persist status version bump between steps
        self._bump_status_version(status_fp)

        # Step 2: Vision Analysis
        vision_agent = make_vision_agent(runs_dir=run_dir.parent)
        
        # Execute vision analysis in a separate thread
        try:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                def run_vision_in_thread():
                    asyncio.run(self._run_vision(vision_agent, run_dir, query, region))
                future = executor.submit(run_vision_in_thread)
                future.result()  # Wait for completion
        except Exception as e:
            raise RuntimeError(f"Vision step failed: {str(e)}") from e

        # Persist status version bump between steps
        self._bump_status_version(status_fp)

        # Step 3: Synthesis (concise)
        synth_agent = make_synthesis_agent(runs_dir=run_dir.parent)
        
        # Execute synthesis in a separate thread
        try:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                def run_synthesis_in_thread():
                    asyncio.run(self._run_synthesis(synth_agent, run_dir, query, region, "concise"))
                future = executor.submit(run_synthesis_in_thread)
                future.result()  # Wait for completion
        except Exception as e:
            raise RuntimeError(f"Synthesis step failed: {str(e)}") from e

        # Track query for historical analysis and monitoring
        if self.query_tracker:
            try:
                self.query_tracker.track_query(
                    query=query,
                    region=region,
                    run_id=run_dir.name,
                    artifacts_dir=run_dir / "artifacts"
                )
            except Exception:
                pass  # Soft fail - don't break workflow if tracking fails

        # Generate dashboard-ready output
        if DashboardFormatter:
            try:
                dashboard_data = DashboardFormatter.format_artifacts(
                    artifacts_dir=run_dir / "artifacts",
                    run_id=run_dir.name
                )
                
                # Write dashboard.json for frontend consumption
                dashboard_path = run_dir / "artifacts" / "dashboard.json"
                with open(dashboard_path, 'w', encoding='utf-8') as f:
                    json.dump(dashboard_data.to_dict(), f, indent=2, ensure_ascii=False)
                    
            except Exception:
                pass  # Soft fail - don't break workflow if dashboard formatting fails

        # On success, the route marks succeeded. If exceptions occur, caller handles state to failed.

    async def _run_recon(self, agent, run_dir: Path, query: str, region: str) -> None:
        await agent.run(run_dir=run_dir, query=query, region=region, max_results=10)

    async def _run_vision(self, agent, run_dir: Path, query: str, region: str) -> None:
        await agent.run(run_dir=run_dir, query=query, region=region)

    async def _run_synthesis(self, agent, run_dir: Path, query: str, region: str, style: str) -> None:
        await agent.run(run_dir=run_dir, query=query, region=region, style=style)

    @staticmethod
    def _bump_status_version(status_fp: Path) -> None:
        try:
            status = RunStatus.from_file(status_fp)
            # use a benign transition if still running to bump version and updated_at
            if status.state == RunPhase.running:
                # emulate a no-op by transitioning running->running is invalid; instead, increment version directly
                # while keeping state intact; retain timestamps to avoid confusion
                d = status.to_dict()
                d["version"] = int(d.get("version", 0)) + 1
                atomic_write_json(status_fp, d)
        except Exception:
            # non-fatal
            pass