from __future__ import annotations

import time
from typing import Any, Dict, List, Optional, Tuple

from .utils import RetryConfig, retry_call, concurrent_map, append_audit, write_status
from ..services.youtube_service import YouTubeService
from ..services.trends_service import TrendsService
from ..services.transcripts_service import TranscriptsService
from ..services.vision_service import VisionService


def step_search_videos(run_dir_path, yt: YouTubeService, query: str, max_results: int, retry_cfg: RetryConfig) -> List[Dict[str, Any]]:
    def call() -> List[Dict[str, Any]]:
        return yt.search_videos(query, max_results=max_results)

    res, err = retry_call(call, retry_cfg)
    if err:
        append_audit(run_dir_path, {
            "ts": time.time(),
            "subsystem": "youtube",
            "stage": "search_videos",
            "reason": err,
            "retries": retry_cfg.attempts,
            "outcome": "failed" if res is None else "partial"
        })
    return res or []


def step_video_details(run_dir_path, yt: YouTubeService, video_ids: List[str], retry_cfg: RetryConfig) -> List[Dict[str, Any]]:
    def call() -> List[Dict[str, Any]]:
        return yt.get_video_details(video_ids)

    res, err = retry_call(call, retry_cfg)
    if err:
        append_audit(run_dir_path, {
            "ts": time.time(),
            "subsystem": "youtube",
            "stage": "video_details",
            "reason": err,
            "retries": retry_cfg.attempts,
            "outcome": "failed" if res is None else "partial"
        })
    return res or []


def step_channel_details(run_dir_path, yt: YouTubeService, channel_ids: List[str], retry_cfg: RetryConfig) -> List[Dict[str, Any]]:
    def call() -> List[Dict[str, Any]]:
        return yt.get_channel_details(channel_ids)

    res, err = retry_call(call, retry_cfg)
    if err:
        append_audit(run_dir_path, {
            "ts": time.time(),
            "subsystem": "youtube",
            "stage": "channel_details",
            "reason": err,
            "retries": retry_cfg.attempts,
            "outcome": "failed" if res is None else "partial"
        })
    return res or []


def step_comments_for_top_videos(
    run_dir_path,
    yt: YouTubeService,
    video_details: List[Dict[str, Any]],
    top_n: int,
    comments_max_threads: int,
    retry_cfg: RetryConfig,
) -> Dict[str, Any]:
    def _views(v: Dict[str, Any]) -> int:
        stats_val = v.get("statistics")
        if not isinstance(stats_val, dict):
            return 0
        raw = stats_val.get("viewCount")
        try:
            if raw is None:
                return 0
            return int(str(raw))
        except Exception:
            return 0

    ranked = sorted(video_details, key=_views, reverse=True)[: max(0, int(top_n))]
    top_ids: List[str] = []
    for v in ranked:
        vid_val = v.get("id")
        if isinstance(vid_val, str) and vid_val:
            top_ids.append(vid_val)

    results: Dict[str, Any] = {}
    for vid in top_ids:
        def call() -> List[Dict[str, Any]]:
            return yt.get_comment_threads(vid, max_threads=comments_max_threads, order="relevance")
        res, err = retry_call(call, retry_cfg)
        if err:
            append_audit(run_dir_path, {
                "ts": time.time(),
                "subsystem": "youtube",
                "stage": "comment_threads",
                "video_id": vid,
                "reason": err,
                "retries": retry_cfg.attempts,
                "outcome": "failed" if res is None else "partial"
            })
        results[vid] = {"items": res or []}
    return results


def step_trends(run_dir_path, trends: TrendsService, query: str, region: str, retry_cfg: RetryConfig) -> Dict[str, Any]:
    iot: List[Dict[str, Any]] = []
    rel: Dict[str, List[Dict[str, Any]]] = {"top": [], "rising": []}

    # interest over time
    def call_iot() -> List[Dict[str, Any]]:
        return trends.get_interest_over_time(query, region, months=24)
    res_iot, err_iot = retry_call(call_iot, retry_cfg)
    if err_iot:
        append_audit(run_dir_path, {"ts": time.time(), "subsystem": "trends", "stage": "interest_over_time", "reason": err_iot, "retries": retry_cfg.attempts, "outcome": "failed" if res_iot is None else "partial"})
    iot = res_iot or []

    # related queries
    def call_rel() -> Dict[str, List[Dict[str, Any]]]:
        return trends.get_related_queries(query, region, top_limit=15, rising_limit=15)
    res_rel, err_rel = retry_call(call_rel, retry_cfg)
    if err_rel:
        append_audit(run_dir_path, {"ts": time.time(), "subsystem": "trends", "stage": "related_queries", "reason": err_rel, "retries": retry_cfg.attempts, "outcome": "failed" if res_rel is None else "partial"})
    rel = res_rel or {"top": [], "rising": []}

    return {"interest_over_time": iot, "related": rel}


def step_transcripts(run_dir_path, tx: TranscriptsService, video_ids: List[str], concurrency: int, retry_cfg: RetryConfig) -> Dict[str, Optional[str]]:
    def worker(vid: str) -> Optional[str]:
        return tx.get_transcript(vid)

    results: Dict[str, Optional[str]] = {}
    for vid, value, err in concurrent_map(video_ids, worker, max_workers=concurrency, retry_cfg=retry_cfg):
        results[vid] = value
        if err:
            append_audit(run_dir_path, {
                "ts": time.time(),
                "subsystem": "transcripts",
                "stage": "fetch",
                "video_id": vid,
                "reason": err,
                "retries": retry_cfg.attempts,
                "outcome": "missing" if value is None else "ok",
            })
    return results


def step_vision(run_dir_path, vision: VisionService, urls: List[str], concurrency: int, retry_cfg: RetryConfig) -> Dict[str, int | bool]:
    def worker(url: str) -> int | bool:
        # VisionService.detect_faces_in_thumbnails handles a list; call with single-item list
        return vision.detect_faces_in_thumbnails([url]).get(url, False)

    results: Dict[str, int | bool] = {}
    for url, value, err in concurrent_map(urls, worker, max_workers=concurrency, retry_cfg=retry_cfg):
        results[url] = value if isinstance(value, (int, bool)) else False
        if err or value is False:
            append_audit(run_dir_path, {
                "ts": time.time(),
                "subsystem": "vision",
                "stage": "detect_faces",
                "thumbnail": url,
                "reason": err or "detection_unavailable_or_failed",
                "retries": retry_cfg.attempts,
                "outcome": "partial" if value is False else "ok",
            })
    return results