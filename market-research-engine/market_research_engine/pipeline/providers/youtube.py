from __future__ import annotations

import os
from typing import Any, Dict, List, Optional, Tuple
import asyncio
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


class YouTubeProvider:
    """
    Minimal YouTube Data API v3 client for Recon step.
    - Uses API key auth via `key` query parameter.
    - Implements search (videos) and videos.list (statistics, snippet).
    - Pagination is supported via pageToken.
    - Network access can be disabled by setting LIVE_PROVIDERS to "false".
    """

    BASE_URL = "https://www.googleapis.com/youtube/v3"

    def __init__(
        self,
        api_key: Optional[str],
        *,
        live: bool = False,
        timeout_s: float = 15.0,
    ) -> None:
        self.api_key = api_key
        self.live = live and bool(api_key)
        self._timeout = timeout_s

    def _ensure_live(self) -> None:
        if not self.live:
            if not self.api_key:
                raise RuntimeError("YouTubeProvider: API key is missing. Set YOUTUBE_API_KEY environment variable.")
            else:
                raise RuntimeError("YouTubeProvider: Live mode is disabled. Set LIVE_PROVIDERS=true to enable.")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError))
    )
    async def search_videos(
        self,
        query: str,
        *,
        region_code: str = "US",
        max_results: int = 10,
        order: str = "relevance",
    ) -> Tuple[List[str], Dict[str, Any]]:
        """
        Returns a tuple of (video_ids, raw_search_response_metadata).
        """
        self._ensure_live()
        params = {
            "part": "snippet",
            "maxResults": max(1, min(max_results, 50)),
            "q": query,
            "type": "video",
            "regionCode": region_code,
            "order": order,
            "key": self.api_key,
        }
        try:
            async with httpx.AsyncClient(timeout=self._timeout) as client:
                r = await client.get(f"{self.BASE_URL}/search", params=params)
                r.raise_for_status()
                data = r.json()
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 403:
                raise RuntimeError(f"YouTube API quota exceeded or invalid API key. Status: {e.response.status_code}")
            elif e.response.status_code == 429:
                raise RuntimeError(f"YouTube API rate limit exceeded. Status: {e.response.status_code}")
            else:
                raise RuntimeError(f"YouTube API error: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            raise RuntimeError(f"YouTube API network error: {str(e)}")

        ids: List[str] = []
        for item in data.get("items", []):
            vid = (((item or {}).get("id") or {}).get("videoId"))
            if isinstance(vid, str):
                ids.append(vid)

        return ids, {"raw": data}

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError))
    )
    async def list_videos_details(self, video_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Fetches snippet and statistics for the given IDs.
        Returns a list of dicts with normalized keys.
        """
        self._ensure_live()
        if not video_ids:
            return []
        joined = ",".join(video_ids[:50])
        params = {
            "part": "snippet,statistics",
            "id": joined,
            "key": self.api_key,
            "maxResults": 50,
        }
        try:
            async with httpx.AsyncClient(timeout=self._timeout) as client:
                r = await client.get(f"{self.BASE_URL}/videos", params=params)
                r.raise_for_status()
                data = r.json()
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 403:
                raise RuntimeError(f"YouTube API quota exceeded or invalid API key. Status: {e.response.status_code}")
            elif e.response.status_code == 429:
                raise RuntimeError(f"YouTube API rate limit exceeded. Status: {e.response.status_code}")
            else:
                raise RuntimeError(f"YouTube API error: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            raise RuntimeError(f"YouTube API network error: {str(e)}")

        out: List[Dict[str, Any]] = []
        for item in data.get("items", []):
            id_ = item.get("id")
            snippet = item.get("snippet", {}) or {}
            stats = item.get("statistics", {}) or {}
            tags = snippet.get("tags") if isinstance(snippet.get("tags"), list) else None
            thumb = None
            thumbs = snippet.get("thumbnails") or {}
            # Select a reasonable default thumbnail
            for key in ("maxres", "standard", "high", "medium", "default"):
                if thumbs.get(key) and thumbs[key].get("url"):
                    thumb = thumbs[key]["url"]
                    break
            out.append(
                {
                    "video_id": id_,
                    "title": snippet.get("title") or "",
                    "channel_id": snippet.get("channelId"),
                    "channel_title": snippet.get("channelTitle"),
                    "published_at": snippet.get("publishedAt"),
                    "description": snippet.get("description"),
                    "tags": tags,
                    "stats": {
                        "view_count": stats.get("viewCount"),
                        "like_count": stats.get("likeCount"),
                        "comment_count": stats.get("commentCount"),
                    },
                    "url": f"https://www.youtube.com/watch?v={id_}" if id_ else None,
                    "thumbnail_url": thumb,
                }
            )
        return out

    @classmethod
    def from_env(cls) -> "YouTubeProvider":
        key = os.getenv("YOUTUBE_API_KEY")
        live = (os.getenv("LIVE_PROVIDERS", "false").lower() == "true")
        return cls(api_key=key, live=live)