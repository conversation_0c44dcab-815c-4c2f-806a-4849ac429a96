from __future__ import annotations

from typing import Any, Dict, List, Optional, Tuple


class FakeYouTubeProvider:
    """
    Deterministic fake for CI and local dev when LIVE_PROVIDERS=false.
    Produces a small, consistent set of items for a given query.
    """

    def __init__(self) -> None:
        pass

    def _generate_fake_transcript(self, video_id: str, index: int) -> str:
        """Generate deterministic fake transcript content for testing."""
        templates = [
            "Hey everyone, welcome back to my channel! Today we're going to talk about {topic}. This is something I've been working on for months and I can't wait to share these insights with you. First, let me show you the most common mistakes people make...",
            "What's up guys! In this video, I'll be covering the top strategies for {topic}. I've personally tested all of these techniques and I can guarantee they work. Let's jump right into tip number one...",
            "If you're struggling with {topic}, this video is for you. I'm going to break down everything you need to know in simple steps. By the end of this video, you'll have a clear roadmap to success...",
            "Welcome to another tutorial! Today's focus is {topic}. I know this can be overwhelming, so I'll make it as simple as possible. Here are the exact steps I use every single day...",
            "Quick question before we start - how many of you have tried {topic} but didn't get the results you wanted? Don't worry, I've been there too. Today I'm sharing what actually works..."
        ]
        
        # Use modulo to cycle through templates deterministically
        template = templates[index % len(templates)]
        topic = video_id.replace("vid_", "").replace("_", " ")
        
        return template.format(topic=topic)

    async def search_videos(
        self,
        query: str,
        *,
        region_code: str = "US",
        max_results: int = 10,
        order: str = "relevance",
    ) -> Tuple[List[str], Dict[str, Any]]:
        sample_ids = [f"vid_{i}_{query.replace(' ', '_')[:12]}" for i in range(1, 1 + min(5, max_results))]
        meta = {"fake": True, "query": query, "region_code": region_code, "order": order}
        return sample_ids, meta

    async def list_videos_details(self, video_ids: List[str]) -> List[Dict[str, Any]]:
        out: List[Dict[str, Any]] = []
        for i, vid in enumerate(video_ids):
            out.append(
                {
                    "video_id": vid,
                    "title": f"Sample Title {i+1} for {vid}",
                    "channel_id": f"chan_{i+1}",
                    "channel_title": f"Channel {i+1}",
                    "published_at": "2024-01-01T00:00:00Z",
                    "description": "This is a fake description used for testing.",
                    "tags": ["test", "sample", "fake"],
                    "stats": {
                        "view_count": 1000 * (i + 1),
                        "like_count": 50 * (i + 1),
                        "comment_count": 10 * (i + 1),
                    },
                    "url": f"https://www.youtube.com/watch?v={vid}",
                    "thumbnail_url": f"https://img.youtube.com/vi/{vid}/maxresdefault.jpg",
                    "thumbnails": {
                        "default": {"url": f"https://img.youtube.com/vi/{vid}/default.jpg"},
                        "medium": {"url": f"https://img.youtube.com/vi/{vid}/mqdefault.jpg"},  
                        "high": {"url": f"https://img.youtube.com/vi/{vid}/hqdefault.jpg"},
                        "maxres": {"url": f"https://img.youtube.com/vi/{vid}/maxresdefault.jpg"}
                    },
                    "transcript_snippet": None,  # filled by fake transcript provider below
                    "full_transcript": self._generate_fake_transcript(vid, i),  # Add full transcript for analysis
                }
            )
        return out


class FakeTranscriptProvider:
    """
    Deterministic transcript snippet generator for CI.
    """

    def __init__(self) -> None:
        pass

    def get_transcript_snippet(self, video_id: str, *, max_chars: int = 600) -> Optional[str]:
        base = f"Transcript for {video_id}: tips, tactics, trends, and pitfalls."
        return base[: max_chars] if base else None


class FakeOpenRouterLLMProvider:
    """
    Deterministic synthesis generator for CI. Ignores inputs and returns a stable JSON shape.
    """

    def __init__(self) -> None:
        pass

    async def synthesize_concise(self, *, query: str, region: str, items: List[Dict[str, Any]], vision_data: Dict[str, Any] = None, transcript_analysis: Dict[str, Any] = None) -> Dict[str, Any]:
        # Build a small deterministic synthesis payload with vision insights
        vision_insights = ""
        if vision_data and vision_data.get("vision_analysis"):
            vision_analysis = vision_data["vision_analysis"]
            faceless_data = vision_analysis.get("faceless_content", {})
            viability_score = faceless_data.get("faceless_viability_score", 50)
            faceless_count = faceless_data.get("faceless_videos_count", 0)
            
            if viability_score > 60:
                vision_insights = f" Strong faceless content opportunity detected with {faceless_count} faceless videos and {viability_score:.1f}/100 viability score."
            elif viability_score > 30:
                vision_insights = f" Moderate faceless content potential with {faceless_count} faceless videos and {viability_score:.1f}/100 viability score."
            else:
                vision_insights = f" Limited faceless content opportunity with {faceless_count} faceless videos and {viability_score:.1f}/100 viability score."

        # Add transcript insights
        transcript_insights = ""
        if transcript_analysis:
            themes = transcript_analysis.get("content_themes", [])
            coverage = transcript_analysis.get("transcript_availability", {})
            transcript_count = coverage.get("videos_with_transcripts", 0)
            
            if transcript_count > 0:
                top_theme = themes[0] if themes else "general topics"
                transcript_insights = f" Transcript analysis of {transcript_count} videos reveals '{top_theme}' as the dominant theme."
        
        recommendations = [
            "Publish a 7–10 min explainer covering the top 3 emerging subtopics",
            "Create a short-form series with tactical tips derived from high-retention snippets",
            "Design thumbnails with bold, high-contrast titles; test 2 variants per video",
            "Bundle related videos into a weekly roundup to drive session time",
            "Collaborate with mid-size channels surfacing the same subtopics",
        ]
        
        # Add vision-specific recommendations if applicable
        if vision_data and vision_data.get("vision_analysis"):
            faceless_data = vision_data["vision_analysis"].get("faceless_content", {})
            viability_score = faceless_data.get("faceless_viability_score", 50)
            
            if viability_score > 50:
                recommendations.insert(2, "Consider faceless content format - analysis shows good viability for screen recordings and animations")

        # Add transcript-specific recommendations
        if transcript_analysis:
            hooks = transcript_analysis.get("successful_hooks", [])
            keywords = transcript_analysis.get("actionable_keywords", [])
            
            if hooks:
                recommendations.append(f"Use proven hook patterns: {hooks[0]}" if hooks else "")
            if keywords:
                recommendations.append(f"Target high-signal keywords: {', '.join(keywords[:3])}")
            
        return {
            "summary": f"Concise brief for '{query}' in {region}. This is a deterministic fake summary.{vision_insights}{transcript_insights}",
            "recommendations": recommendations,
            "blueprint": [
                {"heading": "Focus Areas", "body": "Target subtopics A, B, C; emphasize unique POV and pacing."},
                {"heading": "Format and Cadence", "body": "2x weekly long-form, 3x shorts; keep retention hooks early."},
                {"heading": "Audience Pain Points", "body": "Clarify key misunderstandings and offer clear, repeatable steps."},
            ],
        }

    async def analyze_transcripts(self, *, query: str, transcripts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate fake transcript analysis for testing."""
        # Count videos with transcripts
        transcript_count = sum(1 for item in transcripts_data if item.get("full_transcript"))
        
        return {
            "content_themes": [
                f"{query} fundamentals and basics",
                "Common mistakes and how to avoid them",
                "Advanced strategies and techniques",
                "Tools and software recommendations",
                "Success stories and case studies"
            ],
            "successful_hooks": [
                "Starting with a surprising statistic or fact",
                "Asking a provocative question",
                "Promising specific actionable outcomes"
            ],
            "pain_points": [
                "Feeling overwhelmed by too many options",
                "Not knowing where to start",
                "Struggling to see consistent results",
                "Time constraints and efficiency concerns"
            ],
            "content_formats": [
                "Step-by-step tutorials with screen sharing",
                "Mistake analysis with before/after examples",
                "Tool comparison and review format",
                "Q&A addressing common questions"
            ],
            "actionable_keywords": [
                f"{query}_tutorial", f"{query}_guide", "how_to", "step_by_step",
                "beginners", "mistakes", "tools", "strategy", "results", "tips"
            ],
            "engagement_patterns": f"Videos with practical demonstrations and clear actionable steps perform better. Content that addresses specific pain points in {query} drives higher engagement and retention.",
            "content_gaps": [
                "Beginner-friendly comprehensive guides",
                "Industry-specific use cases and examples"
            ]
        }


class FakeVisionProvider:
    """
    Deterministic vision analysis generator for CI. 
    Produces consistent face detection results for testing.
    """

    def __init__(self) -> None:
        pass

    async def analyze_thumbnails(self, urls: List[str]) -> Dict[str, Any]:
        """Generate fake face detection results for thumbnail URLs."""
        face_detection = {}
        
        # Generate deterministic face counts based on URL hash
        for i, url in enumerate(urls):
            # Make some videos faceless (useful for testing faceless viability)
            if i % 3 == 0:  # Every third video is faceless
                face_detection[url] = 0
            elif i % 3 == 1:  # One face
                face_detection[url] = 1  
            else:  # Multiple faces
                face_detection[url] = 2
        
        total_analyzed = len(urls)
        faceless_count = sum(1 for count in face_detection.values() if count == 0)
        faceless_ratio = faceless_count / total_analyzed if total_analyzed > 0 else 0.0
        
        analysis = {
            "total_thumbnails_analyzed": total_analyzed,
            "faceless_count": faceless_count,
            "faceless_ratio": faceless_ratio,
            "face_detection_success_rate": 1.0  # Fake always succeeds
        }
        
        return {
            "face_detection": face_detection,
            "analysis": analysis
        }