from __future__ import annotations

import os
from typing import Any, Dict, List, Optional

import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


CONCISE_PROMPT_TEMPLATE = """You are an expert YouTube market strategist. Given a list of recent videos with metadata, transcript snippets, and visual analysis data, produce a concise (1–2 page) opportunity brief tailored to the query and region.

Guidelines:
- Focus on highest-signal insights: audience intent, emerging subtopics, content angles that are underserved, and recommended formats.
- Emphasize actionable recommendations (what to publish next and why).
- Keep it structured with short headings and bullet points.
- Avoid boilerplate; reference evidence from the recon list (titles, stats, transcript hints) without quoting excessively.
- Use vision analysis data to assess faceless content viability and format recommendations.

Inputs:
- query: {query}
- region: {region}
- items (sample):
{items_bulleted}
- vision analysis:
{vision_summary}
- transcript content analysis:
{transcript_summary}

Output JSON schema:
{{
  "summary": "2-4 paragraph executive summary of the opportunity, including faceless content insights.",
  "recommendations": ["list of 5-10 actionable content ideas with rationale, including format recommendations based on visual analysis"],
  "blueprint": [
    {{"heading": "Section heading", "body": "1-3 paragraphs with specifics"}},
    {{"heading": "Audience pain points", "body": "Key problems and how our content solves them"}},
    {{"heading": "Format and cadence", "body": "Recommended video lengths, posting frequency, thumbnails/titles style, faceless content viability"}}
  ]
}}
Produce only valid JSON, no prose outside JSON.
"""


TRANSCRIPT_ANALYSIS_TEMPLATE = """You are an expert content strategist analyzing YouTube video transcripts to identify patterns and opportunities.

Given transcripts from {num_videos} videos in the '{query}' space, extract key insights about successful content patterns.

Guidelines:
- Focus on content themes, hooks, structures, and messaging that drive engagement
- Identify common pain points mentioned across transcripts
- Note successful content formats and presentation styles
- Extract actionable keywords and topics
- Keep insights concise and evidence-based

Transcripts (video_id | views | title | transcript_preview):
{transcript_data}

Output JSON schema:
{{
  "content_themes": ["list of 5-8 recurring themes/topics"],
  "successful_hooks": ["list of 3-5 effective opening patterns"],
  "pain_points": ["list of 3-5 audience problems mentioned"],
  "content_formats": ["list of 3-4 successful content structures"],
  "actionable_keywords": ["list of 8-10 high-signal keywords"],
  "engagement_patterns": "2-3 sentences about what drives engagement based on high-performing transcripts",
  "content_gaps": ["list of 2-3 underserved topics or angles"]
}}

Produce only valid JSON, no prose outside JSON.
"""


class OpenRouterLLMProvider:
    """
    Minimal OpenRouter client to generate synthesis JSON using a concise prompt.
    - Reads OPENROUTER_API_KEY from env.
    - Respects LIVE_PROVIDERS toggle (disabled by default).
    - Model configurable via OPENROUTER_MODEL (defaults to gpt-4o-mini).
    """

    BASE_URL = "https://openrouter.ai/api/v1/chat/completions"

    def __init__(
        self,
        api_key: Optional[str],
        *,
        model: Optional[str] = None,
        live: bool = False,
        timeout_s: float = 30.0,
    ) -> None:
        self.api_key = api_key
        self.model = model or os.getenv("OPENROUTER_MODEL", "gpt-4o-mini")
        self.live = live and bool(api_key)
        self._timeout = timeout_s

    @classmethod
    def from_env(cls) -> "OpenRouterLLMProvider":
        key = os.getenv("OPENROUTER_API_KEY")
        live = (os.getenv("LIVE_PROVIDERS", "false").lower() == "true")
        return cls(api_key=key, model=os.getenv("OPENROUTER_MODEL"), live=live)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError))
    )
    async def synthesize_concise(self, *, query: str, region: str, items: List[Dict[str, Any]], vision_data: Dict[str, Any] = None, transcript_analysis: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Calls the LLM to produce a concise synthesis JSON. If live is disabled, raises RuntimeError.
        """
        if not self.live:
            if not self.api_key:
                raise RuntimeError("OpenRouter: API key is missing. Set OPENROUTER_API_KEY environment variable.")
            else:
                raise RuntimeError("OpenRouter: Live mode is disabled. Set LIVE_PROVIDERS=true to enable.")

        # Build a compact bullet list sample of recon items to fit prompt budget
        bullets: List[str] = []
        for it in items[:8]:
            title = (it.get("title") or "")[:120]
            views = it.get("stats", {}).get("view_count")
            snippet = (it.get("transcript_snippet") or "")[:160]
            bullets.append(f"- {title} | views={views} | {snippet}")
        items_bulleted = "\n".join(bullets) if bullets else "- (no items)"

        # Build vision analysis summary
        vision_summary = "No vision analysis available."
        if vision_data and vision_data.get("vision_analysis"):
            vision_analysis = vision_data["vision_analysis"]
            faceless_data = vision_analysis.get("faceless_content", {})
            thumbnail_data = vision_analysis.get("thumbnail_analysis", {})
            
            viability_score = faceless_data.get("faceless_viability_score", 0)
            faceless_count = faceless_data.get("faceless_videos_count", 0)
            total_analyzed = thumbnail_data.get("total_thumbnails", 0)
            
            vision_summary = f"""Face detection analysis:
- {total_analyzed} thumbnails analyzed
- {faceless_count} faceless videos identified ({faceless_count/max(total_analyzed,1)*100:.1f}%)
- Faceless content viability score: {viability_score:.1f}/100
- Recommendation: {'Strong' if viability_score > 70 else 'Moderate' if viability_score > 40 else 'Limited'} potential for faceless content formats"""

        # Build transcript analysis summary
        transcript_summary = "No transcript analysis available."
        if transcript_analysis:
            themes = transcript_analysis.get("content_themes", [])
            hooks = transcript_analysis.get("successful_hooks", [])
            pain_points = transcript_analysis.get("pain_points", [])
            keywords = transcript_analysis.get("actionable_keywords", [])
            coverage = transcript_analysis.get("transcript_availability", {})
            
            transcript_summary = f"""Content analysis from {coverage.get('videos_with_transcripts', 0)} transcripts:
- Main themes: {', '.join(themes[:5])}
- Successful hooks: {', '.join(hooks[:3])}
- Pain points: {', '.join(pain_points[:3])}
- Key keywords: {', '.join(keywords[:8])}
- Coverage: {coverage.get('transcript_coverage', 0)*100:.1f}% of videos have transcripts"""

        prompt = CONCISE_PROMPT_TEMPLATE.format(
            query=query,
            region=region,
            items_bulleted=items_bulleted,
            vision_summary=vision_summary,
            transcript_summary=transcript_summary,
        )

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": "https://localhost",  # optional per OpenRouter guidelines
            "X-Title": "Market Research Engine",
            "Content-Type": "application/json",
        }
        body = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a helpful strategy assistant."},
                {"role": "user", "content": prompt},
            ],
            "temperature": 0.3,
        }

        try:
            async with httpx.AsyncClient(timeout=self._timeout) as client:
                r = await client.post(self.BASE_URL, headers=headers, json=body)
                r.raise_for_status()
                data = r.json()
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                raise RuntimeError(f"OpenRouter: Invalid API key. Status: {e.response.status_code}")
            elif e.response.status_code == 429:
                raise RuntimeError(f"OpenRouter: Rate limit exceeded. Status: {e.response.status_code}")
            elif e.response.status_code == 402:
                raise RuntimeError(f"OpenRouter: Insufficient credits or payment required. Status: {e.response.status_code}")
            else:
                raise RuntimeError(f"OpenRouter API error: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            raise RuntimeError(f"OpenRouter network error: {str(e)}")

        # Extract text
        txt = ""
        try:
            txt = data["choices"][0]["message"]["content"]
        except Exception:
            txt = ""

        # Try to parse JSON content; if parsing fails, return a minimal structure
        import json
        try:
            parsed = json.loads(txt)
            if isinstance(parsed, dict):
                return parsed
        except Exception:
            pass

        # fallback minimal structure
        return {
            "summary": "LLM returned unstructured content; fallback minimal brief.",
            "recommendations": [],
            "blueprint": [{"heading": "Overview", "body": txt[:1200]}],
        }

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError))
    )
    async def analyze_transcripts(self, *, query: str, transcripts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Batch analyze video transcripts to extract content patterns and insights.
        Cost-effective: processes all transcripts in a single API call.
        """
        if not self.live:
            if not self.api_key:
                raise RuntimeError("OpenRouter: API key is missing. Set OPENROUTER_API_KEY environment variable.")
            else:
                raise RuntimeError("OpenRouter: Live mode is disabled. Set LIVE_PROVIDERS=true to enable.")

        # Build transcript summary for prompt
        transcript_entries = []
        for item in transcripts_data[:15]:  # Limit to avoid token limits
            video_id = item.get("video_id", "unknown")
            views = item.get("view_count", 0)
            title = (item.get("title", ""))[:80]
            transcript = (item.get("full_transcript", ""))[:800]  # Limit transcript length
            
            if transcript:  # Only include videos with transcripts
                transcript_entries.append(f"- {video_id} | {views:,} views | {title} | {transcript}...")

        if not transcript_entries:
            # Return empty analysis if no transcripts available
            return {
                "content_themes": [],
                "successful_hooks": [],
                "pain_points": [],
                "content_formats": [],
                "actionable_keywords": [],
                "engagement_patterns": "No transcripts available for analysis",
                "content_gaps": []
            }

        transcript_data = "\n".join(transcript_entries[:10])  # Top 10 to stay under token limits
        
        prompt = TRANSCRIPT_ANALYSIS_TEMPLATE.format(
            num_videos=len(transcript_entries),
            query=query,
            transcript_data=transcript_data
        )

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": "https://localhost",
            "X-Title": "Market Research Engine",
            "Content-Type": "application/json",
        }
        body = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a helpful content strategy assistant."},
                {"role": "user", "content": prompt},
            ],
            "temperature": 0.3,
        }

        try:
            async with httpx.AsyncClient(timeout=self._timeout) as client:
                r = await client.post(self.BASE_URL, headers=headers, json=body)
                r.raise_for_status()
                data = r.json()
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                raise RuntimeError(f"OpenRouter: Invalid API key. Status: {e.response.status_code}")
            elif e.response.status_code == 429:
                raise RuntimeError(f"OpenRouter: Rate limit exceeded. Status: {e.response.status_code}")
            elif e.response.status_code == 402:
                raise RuntimeError(f"OpenRouter: Insufficient credits. Status: {e.response.status_code}")
            else:
                raise RuntimeError(f"OpenRouter API error: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            raise RuntimeError(f"OpenRouter network error: {str(e)}")

        # Extract and parse response
        try:
            response_text = data["choices"][0]["message"]["content"]
            import json
            parsed = json.loads(response_text)
            if isinstance(parsed, dict):
                return parsed
        except Exception:
            pass

        # Fallback minimal structure
        return {
            "content_themes": ["Content analysis unavailable"],
            "successful_hooks": [],
            "pain_points": [],
            "content_formats": [],
            "actionable_keywords": [],
            "engagement_patterns": "Analysis failed - could not parse LLM response",
            "content_gaps": []
        }