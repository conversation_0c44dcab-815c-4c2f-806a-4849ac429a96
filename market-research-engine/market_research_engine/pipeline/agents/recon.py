from __future__ import annotations

import os
from typing import Any, Dict, List, Optional
from pathlib import Path

from ..providers.youtube import YouTube<PERSON>rovider  # live provider (guarded by LIVE_PROVIDERS)
from ..providers.transcripts import TranscriptProvider  # live transcript (soft fallback)
from ..providers.fakes import Fake<PERSON>ouTubeProvider, FakeTranscriptProvider  # fakes
from ..schemas import build_recon_artifact

# Google Trends integration
try:
    from ...services.trends_service import TrendsService  # type: ignore
except Exception:
    TrendsService = None  # type: ignore


class ReconAgent:
    """
    ReconAgent collects recent/high-signal YouTube videos related to the query and region.
    - In LIVE_PROVIDERS=false mode, uses fakes for deterministic output.
    - In LIVE_PROVIDERS=true and keys available, calls YouTube Data API and youtube-transcript-api.
    - Produces artifacts/recon.json as the normalized recon artifact.
    """

    def __init__(self, *, live: bool, runs_dir: Path) -> None:
        self.live = live
        self.runs_dir = runs_dir

        if self.live:
            self.yt = YouTubeProvider.from_env()
            self.tp = TranscriptProvider(live=True)
            self.trends = TrendsService() if TrendsService else None
        else:
            self.yt = FakeYouTubeProvider()
            self.tp = FakeTranscriptProvider()
            self.trends = None

    def _collect_trends_data(self, query: str, region: str) -> Dict[str, Any]:
        """Collect Google Trends data for market momentum insights."""
        trends_data = {
            "interest_over_time": [],
            "related_queries": {"top": [], "rising": []},
            "metadata": {"source": "google_trends", "available": False}
        }
        
        if not self.trends or not query.strip():
            return trends_data
        
        try:
            # Get interest over time (24 months of data)
            series = self.trends.get_interest_over_time(query=query, geo=region, months=24)
            trends_data["interest_over_time"] = series or []
            
            # Get related queries
            related = self.trends.get_related_queries(query=query, geo=region, top_limit=15, rising_limit=15)
            trends_data["related_queries"] = {
                "top": related.get("top", []),
                "rising": related.get("rising", [])
            }
            
            # Calculate trend velocity (momentum)
            if series and len(series) >= 2:
                recent_avg = sum(point["value"] for point in series[-3:]) / min(3, len(series))
                older_avg = sum(point["value"] for point in series[:3]) / min(3, len(series))
                trend_velocity = ((recent_avg - older_avg) / max(older_avg, 1)) * 100
                trends_data["trend_velocity"] = round(trend_velocity, 2)
            
            trends_data["metadata"]["available"] = True
            
        except Exception as e:
            # Soft fail - keep empty data structure
            trends_data["metadata"]["error"] = str(e)
        
        return trends_data

    async def run(self, *, run_dir: Path, query: str, region: str, max_results: int = 10) -> Path:
        """
        Executes recon and writes artifacts/recon.json atomically.
        """
        artifacts_dir = run_dir / "artifacts"
        artifacts_dir.mkdir(parents=True, exist_ok=True)
        out_path = artifacts_dir / "recon.json"

        # Fetch candidates (IDs + details)
        ids, _meta = await self.yt.search_videos(query, region_code=region, max_results=max_results)
        details = await self.yt.list_videos_details(ids)

        # Fill transcript snippets (best-effort)
        for d in details:
            vid = d.get("video_id")
            if "transcript_snippet" not in d or d.get("transcript_snippet") is None:
                d["transcript_snippet"] = self.tp.get_transcript_snippet(str(vid)) if vid else None

        # Collect Google Trends data
        trends_data = self._collect_trends_data(query, region)

        # Build and persist artifact with trends data
        recon = build_recon_artifact(query=query, region=region, items=details, trends_data=trends_data)
        tmp = out_path.with_suffix(".json.tmp")
        tmp.write_text(recon.to_json(), encoding="utf-8")
        tmp.replace(out_path)
        return out_path


def make_recon_agent(*, runs_dir: Path) -> ReconAgent:
    live = (os.getenv("LIVE_PROVIDERS", "false").lower() == "true")
    return ReconAgent(live=live, runs_dir=runs_dir)