from __future__ import annotations

import os
from typing import Any, Dict, List, Tuple
from pathlib import Path
import json
import re

from ..providers.openrouter_llm import OpenRouterLLMProvider  # live (guarded by LIVE_PROVIDERS)
from ..providers.fakes import FakeOpenRouterLLMProvider  # fake for CI/local
from ..schemas import build_synthesis_artifact


class SynthesisAgent:
    """
    SynthesisAgent consumes recon.json and produces a concise (1–2 page) brief:
    - artifacts/synthesis.json (structured JSON)
    - artifacts/synthesis.md (human-readable)
    In LIVE_PROVIDERS=false, uses a deterministic fake to ensure CI reliability.

    Minimal hardening:
      - Extract/repair JSON when provider returns fenced or inline JSON inside text
      - Detect likely truncation and retry once with tighter constraints
      - Deterministic services-only fallback if still unstructured
    """

    def __init__(self, *, live: bool, runs_dir: Path) -> None:
        self.live = live
        self.runs_dir = runs_dir
        if self.live:
            self.llm = OpenRouterLLMProvider.from_env()
        else:
            self.llm = FakeOpenRouterLLMProvider()
        # Config flags (env-controlled defaults)
        self.enable_repair_json = (os.getenv("SYNTH_ENABLE_REPAIR_JSON", "true").lower() == "true")
        self.enable_retry_on_trunc = (os.getenv("SYNTH_ENABLE_RETRY_ON_TRUNC", "true").lower() == "true")
        try:
            self.retry_max = int(os.getenv("SYNTH_RETRY_MAX", "1"))
        except Exception:
            self.retry_max = 1

    @staticmethod
    def _load_recon_items(recon_path: Path) -> List[Dict[str, Any]]:
        try:
            data = json.loads(recon_path.read_text(encoding="utf-8"))
            items = data.get("items") or []
            return items if isinstance(items, list) else []
        except Exception:
            return []

    @staticmethod
    def _load_vision_data(vision_path: Path) -> Dict[str, Any]:
        """Load vision analysis data from vision.json artifact."""
        try:
            data = json.loads(vision_path.read_text(encoding="utf-8"))
            return data
        except Exception:
            return {}

    @staticmethod
    def _render_markdown(syn: Dict[str, Any]) -> str:
        summary = syn.get("summary") or ""
        recs = syn.get("recommendations") or []
        blueprint = syn.get("blueprint") or []

        md = []
        md.append("# Opportunity Brief (Concise)")
        md.append("")
        md.append("## Summary")
        md.append(summary if isinstance(summary, str) else str(summary))
        md.append("")
        if isinstance(recs, list) and recs:
            md.append("## Recommendations")
            for r in recs:
                md.append(f"- {r}")
            md.append("")
        if isinstance(blueprint, list) and blueprint:
            md.append("## Blueprint")
            for sec in blueprint:
                heading = str(sec.get("heading", "Section"))
                body = str(sec.get("body", ""))
                md.append(f"### {heading}")
                md.append(body)
                md.append("")
        return "\n".join(md).strip() + "\n"

    # ------------------------ Hardening helpers ------------------------

    @staticmethod
    def _extract_json_from_text(text: str) -> Tuple[Dict[str, Any] | None, bool]:
        """
        Attempt to extract a JSON object from arbitrary text.
        Returns (obj, maybe_truncated). maybe_truncated indicates if the source looks cut off.
        Heuristics:
          1) Prefer fenced ```json ... ``` blocks
          2) Else, find the largest balanced {...} block
        """
        if not isinstance(text, str) or not text.strip():
            return None, False

        # 1. Fenced JSON block
        fenced = re.findall(r"```json\\s*([\\s\\S]*?)\\s*```", text, flags=re.IGNORECASE)
        candidates: List[str] = []
        candidates.extend(fenced)

        # 2. Largest balanced {...} block (greedy scan)
        # Find the first '{' and then try to locate a matching '}' by balance
        first_brace = text.find("{")
        if first_brace != -1:
            balance = 0
            end_index = -1
            for i in range(first_brace, len(text)):
                ch = text[i]
                if ch == "{":
                    balance += 1
                elif ch == "}":
                    balance -= 1
                    if balance == 0:
                        end_index = i
                        # continue scanning to potentially get a larger balanced block later
            if end_index != -1:
                candidates.append(text[first_brace : end_index + 1])

        # Try to parse candidates in order
        for raw in candidates:
            raw_stripped = raw.strip()
            # Heuristic: truncate at last complete closing brace to avoid dangling text after a fenced block
            last_close = raw_stripped.rfind("}")
            maybe_truncated = False
            if last_close != -1 and last_close < len(raw_stripped) - 1:
                # extra trailing junk; trim
                raw_stripped = raw_stripped[: last_close + 1]
                maybe_truncated = True
            try:
                obj = json.loads(raw_stripped)
                if isinstance(obj, dict):
                    return obj, maybe_truncated
            except Exception:
                # try a common repair: remove trailing incomplete tokens
                pass

        # If nothing parsed, detect if the entire text looks truncated (ends mid-word or ends with "...")
        maybe_trunc = bool(re.search(r"(\\.\\.\\.|\\S+$)", text.strip())) and not text.strip().endswith("}")
        return None, maybe_trunc

    def _normalize_synthesis(self, synthesis: Any) -> Dict[str, Any]:
        """
        Normalize provider output into expected dict shape or return empty fields.
        Accepts dict or str (attempt extraction).
        """
        data: Dict[str, Any] = {}
        if isinstance(synthesis, dict):
            data = synthesis
        elif isinstance(synthesis, str) and self.enable_repair_json:
            extracted, _ = self._extract_json_from_text(synthesis)
            if isinstance(extracted, dict):
                data = extracted

        # Coerce fields
        summary = str(data.get("summary", "")) if isinstance(data, dict) else ""
        recs = data.get("recommendations") if isinstance(data, dict) else []
        if not isinstance(recs, list):
            recs = []
        recs = [str(r) for r in recs]
        blueprint_raw = data.get("blueprint") if isinstance(data, dict) else []
        blueprint: List[Dict[str, str]] = []
        if isinstance(blueprint_raw, list):
            for sec in blueprint_raw:
                if isinstance(sec, dict):
                    blueprint.append({
                        "heading": str(sec.get("heading", "")),
                        "body": str(sec.get("body", "")),
                    })
        return {"summary": summary, "recommendations": recs, "blueprint": blueprint}

    async def run(self, *, run_dir: Path, query: str, region: str, style: str = "concise") -> Dict[str, Path]:
        artifacts_dir = run_dir / "artifacts"
        artifacts_dir.mkdir(parents=True, exist_ok=True)
        recon_path = artifacts_dir / "recon.json"
        vision_path = artifacts_dir / "vision.json"
        out_json = artifacts_dir / "synthesis.json"
        out_md = artifacts_dir / "synthesis.md"

        items = self._load_recon_items(recon_path)
        vision_data = self._load_vision_data(vision_path)
        
        # Extract transcript analysis from vision data
        transcript_analysis = vision_data.get("transcript_analysis", {}) if vision_data else {}
        
        # Call LLM (or fake) to generate structured JSON with vision and transcript context
        synthesis = await self.llm.synthesize_concise(
            query=query,
            region=region,
            items=items,
            vision_data=vision_data,
            transcript_analysis=transcript_analysis
        )

        # Normalize / repair
        normalized = self._normalize_synthesis(synthesis)
        summary = normalized.get("summary", "")
        recs = normalized.get("recommendations", [])
        blueprint = normalized.get("blueprint", [])

        # If still empty or obviously unstructured, attempt single retry if allowed
        need_retry = self.enable_retry_on_trunc and self.retry_max > 0 and (not summary and not recs and not blueprint)
        attempts = 0
        while need_retry and attempts < self.retry_max and self.live:
            attempts += 1
            # Retry with hint to produce strict JSON only (provider should respect, but we're not modifying provider here)
            retry_payload = await self.llm.synthesize_concise(
                query=query,
                region=region,
                items=items,
                vision_data=vision_data,
                transcript_analysis=transcript_analysis
            )
            normalized = self._normalize_synthesis(retry_payload)
            summary = normalized.get("summary", "")
            recs = normalized.get("recommendations", [])
            blueprint = normalized.get("blueprint", [])
            need_retry = (not summary and not recs and not blueprint) and (attempts < self.retry_max)

        # If still unstructured, use deterministic services-only fallback brief
        if not summary and not recs and not blueprint:
            summary = "Structured synthesis unavailable; generated deterministic brief."
            # Provide a minimal blueprint that points user to check artifacts
            blueprint = [
                {"heading": "Data Note", "body": "Provider returned unstructured content. Using deterministic summary."}
            ]

        artifact = build_synthesis_artifact(
            query=query,
            region=region,
            style=style,
            summary=summary,
            recommendations=recs,
            blueprint=blueprint,
        )

        # Persist JSON atomically
        tmp = out_json.with_suffix(".json.tmp")
        tmp.write_text(artifact.to_json(), encoding="utf-8")
        tmp.replace(out_json)

        # Persist Markdown
        out_md_tmp = out_md.with_suffix(".md.tmp")
        out_md_tmp.write_text(self._render_markdown(json.loads(artifact.to_json())), encoding="utf-8")
        out_md_tmp.replace(out_md)

        return {"json": out_json, "md": out_md}


def make_synthesis_agent(*, runs_dir: Path) -> SynthesisAgent:
    live = (os.getenv("LIVE_PROVIDERS", "false").lower() == "true")
    return SynthesisAgent(live=live, runs_dir=runs_dir)