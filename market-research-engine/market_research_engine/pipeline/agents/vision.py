from __future__ import annotations

import os
from typing import Any, Dict, List, Optional
from pathlib import Path
import json

from ..providers.fakes import <PERSON>akeV<PERSON><PERSON><PERSON>ider, FakeOpenRouterLL<PERSON>rovider  # fake for CI/local
from ..providers.openrouter_llm import OpenRouter<PERSON><PERSON>rovider
from ..schemas import build_vision_artifact


class VisionProvider:
    """Live vision provider for face detection and visual analysis."""
    
    def __init__(self) -> None:
        # Import VisionService lazily to avoid hard failures
        try:
            from ...services.vision_service import VisionService
            self._service = VisionService()
        except Exception:
            self._service = None

    @classmethod
    def from_env(cls) -> 'VisionProvider':
        return cls()

    async def analyze_thumbnails(self, urls: List[str]) -> Dict[str, Any]:
        """Analyze thumbnail URLs for face detection."""
        if not self._service or not urls:
            return {"face_detection": {}, "analysis": {}}
        
        try:
            face_results = self._service.detect_faces_in_thumbnails(urls)
            
            # Calculate analysis metrics
            total_analyzed = len(urls)
            faceless_count = sum(1 for result in face_results.values() 
                               if isinstance(result, int) and result == 0)
            faceless_ratio = faceless_count / total_analyzed if total_analyzed > 0 else 0.0
            
            analysis = {
                "total_thumbnails_analyzed": total_analyzed,
                "faceless_count": faceless_count,
                "faceless_ratio": faceless_ratio,
                "face_detection_success_rate": sum(1 for result in face_results.values() 
                                                 if isinstance(result, int)) / total_analyzed if total_analyzed > 0 else 0.0
            }
            
            return {
                "face_detection": face_results,
                "analysis": analysis
            }
        except Exception:
            return {"face_detection": {}, "analysis": {}}


class VisionAgent:
    """
    VisionAgent analyzes visual content from videos (thumbnails, transcripts).
    - In LIVE_PROVIDERS=false mode, uses fakes for deterministic output.
    - In LIVE_PROVIDERS=true, performs actual face detection on thumbnails.
    - Produces artifacts/vision.json with visual analysis results.
    """

    def __init__(self, *, live: bool, runs_dir: Path) -> None:
        self.live = live
        self.runs_dir = runs_dir

        if self.live:
            self.vision = VisionProvider.from_env()
            self.llm = OpenRouterLLMProvider.from_env()
            # Import transcript service for live mode
            try:
                from ...services.transcripts_service import TranscriptsService
                self.transcript_service = TranscriptsService()
            except Exception:
                self.transcript_service = None
        else:
            self.vision = FakeVisionProvider()
            self.llm = FakeOpenRouterLLMProvider()
            self.transcript_service = None

    @staticmethod
    def _load_recon_items(recon_path: Path) -> List[Dict[str, Any]]:
        """Load video items from recon.json artifact."""
        try:
            data = json.loads(recon_path.read_text(encoding="utf-8"))
            items = data.get("items") or []
            return items if isinstance(items, list) else []
        except Exception:
            return []

    @staticmethod
    def _extract_thumbnail_urls(items: List[Dict[str, Any]]) -> Dict[str, str]:
        """Extract thumbnail URLs from video items, mapping URL to video_id."""
        url_to_video_id = {}
        
        for item in items:
            video_id = item.get("video_id")
            if not video_id:
                continue
                
            # Look for thumbnails in the item structure
            thumbnails = item.get("thumbnails", {})
            if not isinstance(thumbnails, dict):
                continue
                
            # Prefer higher quality thumbnails
            for quality in ["maxres", "high", "medium", "default"]:
                if quality in thumbnails:
                    url = thumbnails[quality].get("url")
                    if url:
                        url_to_video_id[url] = video_id
                        break
                        
        return url_to_video_id

    def _calculate_faceless_viability_score(
        self, 
        face_results: Dict[str, Any], 
        url_to_video_id: Dict[str, str],
        items: List[Dict[str, Any]]
    ) -> float:
        """Calculate faceless content viability score (0-100)."""
        faceless_videos = []
        
        # Identify faceless videos
        for url, face_count in face_results.items():
            video_id = url_to_video_id.get(url)
            if video_id and isinstance(face_count, int) and face_count == 0:
                # Find video performance data
                for item in items:
                    if item.get("video_id") == video_id:
                        faceless_videos.append({
                            "video_id": video_id,
                            "views": item.get("view_count", 0),
                            "likes": item.get("like_count", 0),
                            "engagement": item.get("like_count", 0) / max(item.get("view_count", 1), 1)
                        })
                        break
        
        if not faceless_videos:
            return 0.0
            
        total_videos = len(items)
        faceless_ratio = len(faceless_videos) / max(total_videos, 1)
        
        # Base score from ratio (0-40 points)
        ratio_score = min(faceless_ratio * 100, 40.0)
        
        # Performance score based on average views (0-40 points)
        avg_views = sum(v["views"] for v in faceless_videos) / len(faceless_videos)
        performance_score = min((avg_views / 100000) * 40.0, 40.0)  # Normalize to 100k views
        
        # Engagement score (0-20 points)
        avg_engagement = sum(v["engagement"] for v in faceless_videos) / len(faceless_videos)
        engagement_score = min(avg_engagement * 1000, 20.0)  # Normalize engagement rate
        
        return min(ratio_score + performance_score + engagement_score, 100.0)

    def _calculate_market_staleness_index(
        self, 
        items: List[Dict[str, Any]], 
        trends_data: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Calculate Market Staleness Index (0-100).
        Higher score = more saturated/stale market
        Lower score = fresher market with opportunities
        """
        if not items:
            return 50.0  # Neutral score
        
        staleness_factors = []
        
        # Factor 1: Title repetition/similarity (0-30 points)
        titles = [item.get("title", "").lower() for item in items]
        unique_words = set()
        total_words = 0
        
        for title in titles:
            words = title.split()
            total_words += len(words)
            unique_words.update(words)
        
        word_repetition = 1.0 - (len(unique_words) / max(total_words, 1))
        title_staleness = min(word_repetition * 100, 30.0)
        staleness_factors.append(title_staleness)
        
        # Factor 2: Content age concentration (0-25 points)
        # Check if most content is from same time period
        publish_dates = []
        for item in items:
            pub_date = item.get("published_at")
            if pub_date:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(pub_date.replace('Z', '+00:00'))
                    publish_dates.append(dt)
                except:
                    pass
        
        if len(publish_dates) > 1:
            # Calculate time span and concentration
            sorted_dates = sorted(publish_dates)
            time_span = (sorted_dates[-1] - sorted_dates[0]).days
            
            # If most content is from same month, it's stale
            if time_span < 30:  # All within 30 days
                age_staleness = 25.0
            elif time_span < 90:  # All within 3 months  
                age_staleness = 15.0
            else:
                age_staleness = 0.0
        else:
            age_staleness = 10.0  # Neutral if can't determine
            
        staleness_factors.append(age_staleness)
        
        # Factor 3: Performance homogeneity (0-25 points)
        # If all videos have similar performance, market is saturated
        view_counts = [item.get("view_count", 0) for item in items if item.get("view_count")]
        
        if len(view_counts) > 2:
            import statistics
            try:
                mean_views = statistics.mean(view_counts)
                stdev_views = statistics.stdev(view_counts) if len(view_counts) > 1 else 0
                coefficient_variation = stdev_views / max(mean_views, 1)
                
                # Low variation = homogeneous = stale
                if coefficient_variation < 0.3:
                    performance_staleness = 25.0
                elif coefficient_variation < 0.6:
                    performance_staleness = 15.0
                else:
                    performance_staleness = 5.0
            except:
                performance_staleness = 12.5
        else:
            performance_staleness = 12.5
            
        staleness_factors.append(performance_staleness)
        
        # Factor 4: Trend momentum (0-20 points)
        trend_staleness = 10.0  # Default neutral
        if trends_data and trends_data.get("interest_over_time"):
            series = trends_data["interest_over_time"]
            if len(series) >= 3:
                recent_values = [point.get("value", 0) for point in series[-3:]]
                older_values = [point.get("value", 0) for point in series[:3]]
                
                recent_avg = sum(recent_values) / len(recent_values)
                older_avg = sum(older_values) / len(older_values)
                
                # Declining trend = stale market
                if recent_avg < older_avg * 0.7:
                    trend_staleness = 20.0
                elif recent_avg < older_avg * 0.9:
                    trend_staleness = 15.0
                elif recent_avg > older_avg * 1.2:
                    trend_staleness = 0.0  # Growing market = fresh
                else:
                    trend_staleness = 8.0
        
        staleness_factors.append(trend_staleness)
        
        # Calculate final staleness index
        total_staleness = sum(staleness_factors)
        return min(total_staleness, 100.0)

    def _prepare_video_selection_data(
        self,
        items: List[Dict[str, Any]],
        transcript_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Prepare video data optimized for dashboard selection and analyzer handoff.
        
        Returns structured data that makes it easy for users to:
        1. Browse and select videos for deep analysis
        2. Understand which videos are most valuable to analyze
        3. Pass selected videos to the video analyzer seamlessly
        """
        selection_videos = []
        
        for item in items:
            video_id = item.get("video_id", "")
            stats = item.get("stats", {})
            
            # Handle transcript data safely
            transcript_snippet = item.get("transcript_snippet") or ""
            full_transcript = item.get("full_transcript") or ""
            has_transcript = bool(transcript_snippet or full_transcript)
            transcript_length = len(transcript_snippet or full_transcript)
            
            # Calculate analysis potential score (0-100)
            analysis_potential = self._calculate_analysis_potential(item, transcript_analysis)
            
            # Extract key metadata for selection
            video_data = {
                "video_id": video_id,
                "title": item.get("title", ""),
                "channel_title": item.get("channel_title", ""),
                "url": f"https://www.youtube.com/watch?v={video_id}" if video_id else "",
                "thumbnail_url": item.get("thumbnail_url", ""),
                "published_at": item.get("published_at", ""),
                
                # Performance metrics
                "metrics": {
                    "view_count": stats.get("view_count", 0),
                    "like_count": stats.get("like_count", 0),  
                    "comment_count": stats.get("comment_count", 0),
                    "engagement_rate": self._calculate_engagement_rate(stats),
                },
                
                # Analysis readiness
                "analysis_readiness": {
                    "potential_score": analysis_potential,
                    "has_transcript": has_transcript,
                    "transcript_length": transcript_length,
                    "has_thumbnail": bool(item.get("thumbnail_url")),
                },
                
                # Selection metadata
                "selection_metadata": {
                    "recommended_for_analysis": analysis_potential >= 70,
                    "analysis_priority": self._get_analysis_priority(analysis_potential),
                    "estimated_analysis_depth": self._estimate_analysis_depth(item),
                    "unique_insights_potential": self._assess_unique_insights(item, transcript_analysis),
                }
            }
            
            selection_videos.append(video_data)
        
        # Sort by analysis potential (best candidates first)
        selection_videos.sort(key=lambda x: x["analysis_readiness"]["potential_score"], reverse=True)
        
        # Generate selection summary
        total_videos = len(selection_videos)
        recommended_count = sum(1 for v in selection_videos if v["selection_metadata"]["recommended_for_analysis"])
        avg_potential = sum(v["analysis_readiness"]["potential_score"] for v in selection_videos) / max(total_videos, 1)
        
        return {
            "videos": selection_videos,
            "selection_summary": {
                "total_videos": total_videos,
                "recommended_for_analysis": recommended_count,
                "average_analysis_potential": round(avg_potential, 1),
                "top_candidate": selection_videos[0]["video_id"] if selection_videos else None,
            },
            "handoff_ready": True,
            "analyzer_compatible": True
        }
    
    def _calculate_analysis_potential(self, item: Dict[str, Any], transcript_analysis: Dict[str, Any]) -> float:
        """Calculate how valuable this video would be for deep analysis (0-100)."""
        score_factors = []
        
        # Performance factor (0-30 points)
        stats = item.get("stats", {})
        view_count = stats.get("view_count", 0)
        if view_count > 100000:
            performance_score = 30.0
        elif view_count > 10000:
            performance_score = 20.0
        elif view_count > 1000:
            performance_score = 10.0
        else:
            performance_score = 5.0
        score_factors.append(performance_score)
        
        # Content richness factor (0-25 points)
        transcript_snippet = item.get("transcript_snippet") or ""
        full_transcript = item.get("full_transcript") or ""
        has_transcript = bool(transcript_snippet or full_transcript)
        transcript_length = len(transcript_snippet or full_transcript)
        
        if has_transcript and transcript_length > 500:
            content_score = 25.0
        elif has_transcript and transcript_length > 200:
            content_score = 15.0
        elif has_transcript:
            content_score = 10.0
        else:
            content_score = 2.0
        score_factors.append(content_score)
        
        # Engagement factor (0-20 points)
        engagement_rate = self._calculate_engagement_rate(stats)
        if engagement_rate > 0.05:  # 5%+
            engagement_score = 20.0
        elif engagement_rate > 0.02:  # 2%+
            engagement_score = 15.0
        elif engagement_rate > 0.01:  # 1%+
            engagement_score = 10.0
        else:
            engagement_score = 5.0
        score_factors.append(engagement_score)
        
        # Uniqueness factor (0-25 points)
        title = item.get("title", "").lower()
        
        # Check if video appears to have unique angle
        unique_indicators = ["secret", "truth", "exposed", "revealed", "behind", "real", "actually", "mistake", "wrong"]
        unique_score = 15.0 if any(indicator in title for indicator in unique_indicators) else 8.0
        score_factors.append(unique_score)
        
        return min(sum(score_factors), 100.0)
    
    def _calculate_engagement_rate(self, stats: Dict[str, Any]) -> float:
        """Calculate engagement rate (likes/views)."""
        views = stats.get("view_count", 0)
        likes = stats.get("like_count", 0)
        return likes / max(views, 1) if views > 0 else 0.0
    
    def _get_analysis_priority(self, potential_score: float) -> str:
        """Get analysis priority level based on potential score."""
        if potential_score >= 80:
            return "high"
        elif potential_score >= 60:
            return "medium"
        else:
            return "low"
    
    def _estimate_analysis_depth(self, item: Dict[str, Any]) -> str:
        """Estimate how deep the analysis could be for this video."""
        transcript_snippet = item.get("transcript_snippet") or ""
        full_transcript = item.get("full_transcript") or ""
        has_transcript = bool(transcript_snippet or full_transcript)
        view_count = item.get("stats", {}).get("view_count", 0)
        
        if has_transcript and view_count > 50000:
            return "comprehensive"
        elif has_transcript or view_count > 10000:
            return "detailed"
        else:
            return "basic"
    
    def _assess_unique_insights(self, item: Dict[str, Any], transcript_analysis: Dict[str, Any]) -> str:
        """Assess potential for unique insights from this video."""
        title = item.get("title", "").lower()
        view_count = item.get("stats", {}).get("view_count", 0)
        
        # Check for breakthrough performance or unique angle
        if view_count > 500000:
            return "high_performer"
        elif any(word in title for word in ["new", "latest", "2024", "2025", "breakthrough", "revolutionary"]):
            return "trending_topic"
        elif any(word in title for word in ["mistake", "wrong", "avoid", "don't", "never"]):
            return "contrarian_view"
        else:
            return "standard"

    async def _fetch_transcripts(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Fetch full transcripts for video items."""
        enriched_items = []
        
        for item in items:
            video_id = item.get("video_id")
            enriched_item = item.copy()
            
            # Check if transcript already exists in item (from fake provider or recon)
            full_transcript = item.get("full_transcript")
            
            # If no full transcript, use transcript_snippet from recon (600 chars)
            if not full_transcript:
                full_transcript = item.get("transcript_snippet")
            
            # If still no transcript, try to fetch via transcript service (live mode) with retry
            if not full_transcript and self.transcript_service and video_id:
                try:
                    import time
                    import random
                    # Add delay to avoid rate limits
                    time.sleep(random.uniform(0.5, 1.5))
                    full_transcript = self.transcript_service.get_transcript(video_id)
                except Exception as e:
                    # Log the error but continue
                    print(f"Warning: Could not fetch transcript for {video_id}: {e}")
                    pass  # Soft fail
            
            # Add transcript data
            enriched_item["full_transcript"] = full_transcript
            enriched_item["has_transcript"] = bool(full_transcript)
            
            # Normalize field names from recon data structure
            stats = enriched_item.get("stats", {})
            if stats:
                enriched_item["view_count"] = stats.get("view_count", 0)
                enriched_item["like_count"] = stats.get("like_count", 0)
                enriched_item["comment_count"] = stats.get("comment_count", 0)
            
            enriched_items.append(enriched_item)
        
        return enriched_items

    async def _analyze_transcript_patterns(self, query: str, items_with_transcripts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze transcript patterns using LLM."""
        # Filter items that have transcripts and good performance metrics
        transcript_items = [
            item for item in items_with_transcripts 
            if item.get("full_transcript") and item.get("view_count", 0) > 0
        ]
        
        # Debug: print what we found
        print(f"Debug: {len(items_with_transcripts)} total items, {len(transcript_items)} with transcripts")
        for item in items_with_transcripts[:3]:  # Show first 3 for debugging
            has_transcript = bool(item.get("full_transcript"))
            views = item.get("view_count", 0)
            print(f"  Video {item.get('video_id', 'unknown')}: transcript={has_transcript}, views={views}")
        
        if not transcript_items:
            return {
                "content_themes": [],
                "successful_hooks": [],
                "pain_points": [],
                "content_formats": [],
                "actionable_keywords": [],
                "engagement_patterns": "No transcripts available for analysis",
                "content_gaps": [],
                "transcript_availability": {
                    "total_videos": len(items_with_transcripts),
                    "videos_with_transcripts": 0,
                    "transcript_coverage": 0.0
                }
            }
        
        # Sort by performance metrics for better analysis
        transcript_items.sort(key=lambda x: x.get("view_count", 0), reverse=True)
        
        try:
            # Use LLM to analyze transcripts
            analysis = await self.llm.analyze_transcripts(
                query=query,
                transcripts_data=transcript_items
            )
            
            # Add metadata about transcript coverage
            analysis["transcript_availability"] = {
                "total_videos": len(items_with_transcripts),
                "videos_with_transcripts": len(transcript_items),
                "transcript_coverage": len(transcript_items) / len(items_with_transcripts) if items_with_transcripts else 0.0
            }
            
            return analysis
            
        except Exception as e:
            # Fallback if LLM analysis fails
            return {
                "content_themes": ["Transcript analysis failed"],
                "successful_hooks": [],
                "pain_points": [],
                "content_formats": [],
                "actionable_keywords": [],
                "engagement_patterns": f"Transcript analysis error: {str(e)}",
                "content_gaps": [],
                "transcript_availability": {
                    "total_videos": len(items_with_transcripts),
                    "videos_with_transcripts": len(transcript_items),
                    "transcript_coverage": len(transcript_items) / len(items_with_transcripts) if items_with_transcripts else 0.0
                }
            }

    async def run(self, *, run_dir: Path, query: str, region: str) -> Path:
        """
        Execute vision analysis and write artifacts/vision.json atomically.
        """
        artifacts_dir = run_dir / "artifacts"
        artifacts_dir.mkdir(parents=True, exist_ok=True)
        recon_path = artifacts_dir / "recon.json"
        out_path = artifacts_dir / "vision.json"

        # Load video items from recon
        items = self._load_recon_items(recon_path)
        
        # Load trends data from recon  
        trends_data = None
        try:
            recon_data = json.loads(recon_path.read_text(encoding="utf-8"))
            trends_data = recon_data.get("trends_data")
        except Exception:
            pass
        
        # Fetch full transcripts for items
        items_with_transcripts = await self._fetch_transcripts(items)
        
        # Analyze transcript patterns
        transcript_analysis = await self._analyze_transcript_patterns(query, items_with_transcripts)
        
        # Extract thumbnail URLs
        url_to_video_id = self._extract_thumbnail_urls(items)
        thumbnail_urls = list(url_to_video_id.keys())
        
        # Perform vision analysis
        if self.live:
            vision_results = await self.vision.analyze_thumbnails(thumbnail_urls)
        else:
            # Use fake provider
            vision_results = await self.vision.analyze_thumbnails(thumbnail_urls)
        
        face_detection_results = vision_results.get("face_detection", {})
        analysis_metrics = vision_results.get("analysis", {})
        
        # Calculate faceless viability score
        faceless_viability_score = self._calculate_faceless_viability_score(
            face_detection_results, url_to_video_id, items
        )
        
        # Calculate market staleness index
        market_staleness_index = self._calculate_market_staleness_index(items, trends_data)
        
        # Identify top faceless videos
        faceless_videos = []
        for url, face_count in face_detection_results.items():
            video_id = url_to_video_id.get(url)
            if video_id and isinstance(face_count, int) and face_count == 0:
                for item in items:
                    if item.get("video_id") == video_id:
                        faceless_videos.append({
                            "video_id": video_id,
                            "title": item.get("title", ""),
                            "views": item.get("view_count", 0),
                            "url": url
                        })
                        break
        
        # Sort by views and take top 10
        faceless_videos.sort(key=lambda x: x["views"], reverse=True)
        
        # Prepare video selection data for dashboard/analyzer handoff  
        video_selection_data = self._prepare_video_selection_data(items_with_transcripts, transcript_analysis)
        
        # Build and persist artifact
        artifact_data = {
            "query": query,
            "region": region,
            "analysis_type": "vision_and_transcript",
            "vision_analysis": {
                "thumbnail_analysis": {
                    "total_thumbnails": len(thumbnail_urls),
                    "face_detection_results": face_detection_results,
                    **analysis_metrics
                },
                "faceless_content": {
                    "faceless_viability_score": faceless_viability_score,
                    "faceless_videos_count": len(faceless_videos),
                    "top_faceless_videos": faceless_videos[:10]
                },
                "market_analysis": {
                    "market_staleness_index": market_staleness_index,
                    "trend_data_available": trends_data is not None
                }
            },
            "transcript_analysis": transcript_analysis,
            "video_selection": video_selection_data,
            "metadata": {
                "provider": "live" if self.live else "fake",
                "vision_service_available": self.live and hasattr(self.vision, '_service') and self.vision._service is not None,
                "transcript_service_available": self.transcript_service is not None,
                "llm_service_available": self.llm is not None
            }
        }
        
        vision_artifact = build_vision_artifact(**artifact_data)
        
        # Write atomically
        tmp = out_path.with_suffix(".json.tmp")
        tmp.write_text(vision_artifact.to_json(), encoding="utf-8")
        tmp.replace(out_path)
        
        return out_path


def make_vision_agent(*, runs_dir: Path) -> VisionAgent:
    live = (os.getenv("LIVE_PROVIDERS", "false").lower() == "true")
    return VisionAgent(live=live, runs_dir=runs_dir)