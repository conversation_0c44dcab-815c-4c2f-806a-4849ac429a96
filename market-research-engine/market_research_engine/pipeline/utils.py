from __future__ import annotations

import json
import math
import random
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Callable, Dict, Iterable, List, Optional, Tuple, TypeVar

T = TypeVar("T")
R = TypeVar("R")


@dataclass(frozen=True)
class RetryConfig:
    attempts: int = 3
    base_backoff_sec: float = 0.5
    jitter_sec: float = 0.25
    max_sleep_sec: float = 3.0


def jitter_sleep(attempt: int, base: float, jitter: float, max_sleep: float) -> None:
    # attempt starts at 1..N
    expo = base * (2 ** max(0, attempt - 1))
    sleep_for = min(max_sleep, expo + random.uniform(0.0, jitter))
    time.sleep(max(0.0, sleep_for))


def retry_call(func: Callable[[], R], cfg: RetryConfig) -> Tuple[Optional[R], Optional[str]]:
    """
    Execute a callable with retry. Returns (result, error_reason).
    On success, error_reason is None. On failure after attempts, result is None and error_reason is last exception type.
    """
    last_reason: Optional[str] = None
    for i in range(1, max(1, cfg.attempts) + 1):
        try:
            return func(), None
        except Exception as e:  # pragma: no cover - generic defensive
            last_reason = type(e).__name__
            if i >= cfg.attempts:
                break
            jitter_sleep(i, cfg.base_backoff_sec, cfg.jitter_sec, cfg.max_sleep_sec)
    return None, last_reason


def concurrent_map(
    items: Iterable[T],
    worker: Callable[[T], R],
    max_workers: int,
    retry_cfg: Optional[RetryConfig] = None,
) -> List[Tuple[T, Optional[R], Optional[str]]]:
    """
    Concurrent map with per-item retries.
    Returns list of tuples (item, result_or_none, error_reason_or_none).
    """
    retry_cfg = retry_cfg or RetryConfig()
    results: List[Tuple[T, Optional[R], Optional[str]]] = []

    def with_retry(x: T) -> Tuple[T, Optional[R], Optional[str]]:
        def call() -> R:
            return worker(x)

        value, err = retry_call(call, cfg=retry_cfg)
        return (x, value, err)

    with ThreadPoolExecutor(max_workers=max(1, int(max_workers))) as pool:
        futs = {pool.submit(with_retry, it): it for it in items}
        for fut in as_completed(futs):
            original = futs[fut]
            try:
                results.append(fut.result())
            except Exception as e:  # pragma: no cover
                results.append((original, None, type(e).__name__))
    return results


# ---------- Status/Audit helpers ----------

def read_status(run_dir: Path) -> Dict[str, Any]:
    status_path = run_dir / "status.json"
    if not status_path.exists():
        return {}
    try:
        return json.loads(status_path.read_text(encoding="utf-8"))
    except Exception:  # pragma: no cover
        return {}


def write_status(run_dir: Path, patch: Dict[str, Any]) -> None:
    status = read_status(run_dir)
    status.update(patch)
    if "audits" not in status or not isinstance(status.get("audits"), list):
        status["audits"] = []
    status_path = run_dir / "status.json"
    status_path.write_text(json.dumps(status, indent=2), encoding="utf-8")


def append_audit(run_dir: Path, entry: Dict[str, Any]) -> None:
    status = read_status(run_dir)
    audits = status.get("audits")
    if not isinstance(audits, list):
        audits = []
    audits.append(entry)
    status["audits"] = audits
    (run_dir / "status.json").write_text(json.dumps(status, indent=2), encoding="utf-8")


# ---------- Simple stats helpers ----------

def safe_mean(values: List[float]) -> float:
    vals = [float(v) for v in values if isinstance(v, (int, float))]
    if not vals:
        return 0.0
    return sum(vals) / float(len(vals))


def safe_stdev(values: List[float]) -> float:
    vals = [float(v) for v in values if isinstance(v, (int, float))]
    n = len(vals)
    if n < 2:
        return 0.0
    mean = sum(vals) / n
    var = sum((v - mean) ** 2 for v in vals) / (n - 1)
    return math.sqrt(max(0.0, var))


def slope_last(values: List[float], last_n: int = 6) -> float:
    """
    Compute simple slope over last N points using least squares on (t, v),
    where t is 0..(k-1).
    """
    if not values:
        return 0.0
    k = min(last_n, len(values))
    ys = [float(v) for v in values[-k:]]
    xs = list(range(k))
    n = float(k)
    sum_x = sum(xs)
    sum_y = sum(ys)
    sum_xx = sum(x * x for x in xs)
    sum_xy = sum(x * y for x, y in zip(xs, ys))
    denom = n * sum_xx - sum_x * sum_x
    if denom == 0:
        return 0.0
    m = (n * sum_xy - sum_x * sum_y) / denom
    return float(m)


def coef_of_variation(values: List[float]) -> float:
    """
    Coefficient of variation: stdev/mean (0 if mean == 0).
    """
    vals = [float(v) for v in values if isinstance(v, (int, float))]
    if not vals:
        return 0.0
    mu = safe_mean(vals)
    if mu == 0.0:
        return 0.0
    return safe_stdev(vals) / mu


# ---------- Text helpers ----------

def language_mix_from_transcripts(transcripts: Dict[str, Optional[str]]) -> Dict[str, float]:
    """
    Heuristic language mix. For now, classify 'en' when transcript contains mostly ASCII and common stopwords,
    otherwise 'other'. Returns normalized fractions.
    """
    total = 0
    en_hits = 0
    for _vid, txt in transcripts.items():
        if not isinstance(txt, str) or not txt.strip():
            continue
        total += 1
        # Tiny heuristic: if high ascii ratio and common english stopwords present
        ascii_ratio = sum(1 for ch in txt if ord(ch) < 128) / max(1, len(txt))
        lower = txt.lower()
        stop_hits = 0
        for w in (" the ", " and ", " to ", " of ", " is ", " in "):
            if w in lower:
                stop_hits += 1
        if ascii_ratio > 0.9 and stop_hits >= 2:
            en_hits += 1
    if total == 0:
        return {"en": 0.0, "other": 0.0}
    en_frac = en_hits / total
    return {"en": round(en_frac, 3), "other": round(1.0 - en_frac, 3)}


def face_presence_rate(faces_map: Dict[str, int | bool]) -> float:
    """
    Rate of thumbnails with >=1 face among those where detection succeeded.
    """
    counts = [v for v in faces_map.values() if isinstance(v, int)]
    if not counts:
        return 0.0
    positives = len([c for c in counts if c >= 1])
    return round(positives / len(counts), 3)