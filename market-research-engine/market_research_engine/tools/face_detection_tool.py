from __future__ import annotations

from typing import Dict, List, Union

try:
    from ..services.vision_service import VisionService  # type: ignore
except Exception:
    VisionService = None  # type: ignore


class FaceDetectionTool:
    """
    Tool stub: detect faces in a list of thumbnail URLs.

    Proxies to services.vision_service.VisionService.detect_faces_in_thumbnails(urls).
    Returns a mapping { url: face_count | False } where False indicates detection couldn't run.
    TODO: Integrate with agent framework and structured outputs.
    """

    def __init__(self) -> None:
        self._svc = VisionService() if VisionService else None

    def run(self, urls: List[str]) -> Dict[str, Union[int, bool]]:
        if not self._svc or not urls:
            return {}
        try:
            return self._svc.detect_faces_in_thumbnails(urls)
        except Exception:
            return {}