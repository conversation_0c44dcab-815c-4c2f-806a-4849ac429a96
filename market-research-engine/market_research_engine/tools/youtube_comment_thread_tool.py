from __future__ import annotations

from typing import Any, Dict, List

try:
    from ..services.youtube_service import YouTubeService  # type: ignore
except Exception:
    YouTubeService = None  # type: ignore


class YouTubeCommentThreadTool:
    """
    Tool stub: fetch comment threads for a video.

    Calls services.youtube_service.YouTubeService.get_comment_threads(video_id, max_threads, order).
    TODO: Integrate with agent framework and structured outputs.
    """

    def __init__(self) -> None:
        self._svc = YouTubeService() if YouTubeService else None

    def run(self, video_id: str, max_threads: int = 100, order: str = "relevance") -> List[Dict[str, Any]]:
        if not self._svc or not video_id:
            return []
        try:
            return self._svc.get_comment_threads(video_id=video_id, max_threads=max_threads, order=order)
        except Exception:
            return []