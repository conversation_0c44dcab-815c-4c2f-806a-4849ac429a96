from __future__ import annotations

"""
Lightweight placeholder web search tool.

Replace with real integrations (SerpAPI, Tavily, Bing, etc.) when wiring up agents.
"""

from dataclasses import dataclass
from typing import List


@dataclass(frozen=True)
class SearchResult:
    title: str
    url: str
    snippet: str


def search_web(query: str, limit: int = 5) -> List[SearchResult]:
    """
    Placeholder implementation that returns mock results.
    """
    query = query.strip()
    if not query:
        return []

    return [
        SearchResult(
            title=f"Result {i+1} for '{query}'",
            url=f"https://example.com/search?q={query}&rank={i+1}",
            snippet=f"Snippet for '{query}' - item {i+1}",
        )
        for i in range(max(0, limit))
    ]