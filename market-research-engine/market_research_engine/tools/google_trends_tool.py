from __future__ import annotations

from typing import Any, Dict, List

try:
    from ..services.trends_service import TrendsService  # type: ignore
except Exception:
    TrendsService = None  # type: ignore


class GoogleTrendsTool:
    """
    Tool stub: access Google Trends data.

    Provides two entry points that proxy to TrendsService:
    - get_interest_over_time(query, geo, months=24) -> time series (placeholder empty list)
    - get_related_queries(query, geo, top_limit=15, rising_limit=15) -> { "top": [], "rising": [] }

    Note:
    - TrendsService implements a geo_sanity_check and soft-fallback to 'US'.
    - This stub is synchronous and lightweight for the current pass.
    """

    def __init__(self) -> None:
        self._svc = TrendsService() if TrendsService else None

    def get_interest_over_time(self, query: str, geo: str, months: int = 24) -> List[Dict[str, Any]]:
        if not self._svc or not query:
            return []
        try:
            return self._svc.get_interest_over_time(query=query, geo=geo, months=months)
        except Exception:
            return []

    def get_related_queries(
        self, query: str, geo: str, top_limit: int = 15, rising_limit: int = 15
    ) -> Dict[str, List[Dict[str, Any]]]:
        if not self._svc or not query:
            return {"top": [], "rising": []}
        try:
            return self._svc.get_related_queries(
                query=query,
                geo=geo,
                top_limit=top_limit,
                rising_limit=rising_limit,
            )
        except Exception:
            return {"top": [], "rising": []}