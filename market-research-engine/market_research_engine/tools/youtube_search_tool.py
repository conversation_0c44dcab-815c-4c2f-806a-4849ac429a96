from __future__ import annotations

from typing import Any, Dict, List

# Local service wrapper
try:
    from ..services.youtube_service import YouTubeService  # type: ignore
except Exception:
    YouTubeService = None  # type: ignore


class YouTubeSearchTool:
    """
    Tool stub: search YouTube videos.

    Calls services.youtube_service.YouTubeService.search_videos(query, max_results).
    TODO: Integrate with agent framework and structured outputs.
    """

    def __init__(self) -> None:
        self._svc = YouTubeService() if YouTubeService else None

    def run(self, query: str, max_results: int = 30) -> List[Dict[str, Any]]:
        if not self._svc:
            return []
        try:
            return self._svc.search_videos(query=query, max_results=max_results)
        except Exception:
            return []