from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict
from uuid import uuid5, NAMESPACE_URL
import json

from ..config import settings


@dataclass(frozen=True)
class RunInfo:
    run_id: str
    path: Path
    created_at: str


class RunsService:
    def __init__(self, root: Path | None = None) -> None:
        self.root = (root or settings.resolved_runs_dir)
        self.root.mkdir(parents=True, exist_ok=True)

    def create_run(self, project: str, params: Dict[str, Any] | None = None, metadata: Dict[str, Any] | None = None) -> RunInfo:
        params = params or {}
        metadata = metadata or {}

        safe_project = "".join(ch for ch in project if ch.isalnum() or ch in ("-", "_")).strip()
        if not safe_project:
            raise ValueError("Invalid project name")

        ts = datetime.now(timezone.utc).strftime("%Y-%m-%d_%H%M%SZ")
        suffix = uuid5(NAMESPACE_URL, f"{safe_project}:{ts}").hex[:10]
        run_id = f"{ts}-{suffix}"

        run_dir = self.root / run_id
        run_dir.mkdir(parents=True, exist_ok=False)

        created_at = datetime.now(timezone.utc).isoformat()
        meta = {
            "project": project,
            "params": params,
            "metadata": metadata,
            "created_at": created_at,
            "run_id": run_id,
            "run_dir": str(run_dir),
            "app_env": settings.app_env,
        }
        (run_dir / "metadata.json").write_text(json.dumps(meta, indent=2), encoding="utf-8")

        return RunInfo(run_id=run_id, path=run_dir, created_at=created_at)