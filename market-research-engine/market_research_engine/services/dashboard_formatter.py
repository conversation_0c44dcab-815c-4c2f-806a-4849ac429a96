from __future__ import annotations

import json
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional


@dataclass
class DashboardData:
    """Complete dashboard data structure matching the master brief design."""
    
    # Query metadata
    query: str
    region: str
    analysis_date: str
    run_id: str
    
    # Dashboard tabs data
    competition_analysis: Dict[str, Any]
    trending_content: Dict[str, Any]
    opportunities: Dict[str, Any]
    
    # Supporting data
    market_overview: Dict[str, Any]
    video_selection: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "query": self.query,
            "region": self.region,
            "analysis_date": self.analysis_date,
            "run_id": self.run_id,
            "competition_analysis": self.competition_analysis,
            "trending_content": self.trending_content,
            "opportunities": self.opportunities,
            "market_overview": self.market_overview,
            "video_selection": self.video_selection
        }


class DashboardFormatter:
    """
    Formats market research artifacts into dashboard-ready structure.
    
    Transforms raw agent outputs into the three-tab dashboard format:
    - Competition Analysis: Top competitors with performance benchmarks
    - Trending Content: High-velocity recent content  
    - Opportunities: Strategic insights and content gaps
    """
    
    @staticmethod
    def format_artifacts(artifacts_dir: Path, run_id: str) -> DashboardData:
        """Convert all artifacts into dashboard format."""
        
        # Load all artifacts
        recon_data = DashboardFormatter._load_artifact(artifacts_dir / "recon.json")
        vision_data = DashboardFormatter._load_artifact(artifacts_dir / "vision.json")
        synthesis_data = DashboardFormatter._load_artifact(artifacts_dir / "synthesis.json")
        
        query = recon_data.get("query", "Unknown")
        region = recon_data.get("region", "US")
        
        # Format Competition Analysis tab
        competition_analysis = DashboardFormatter._format_competition_analysis(recon_data, vision_data)
        
        # Format Trending Content tab
        trending_content = DashboardFormatter._format_trending_content(recon_data, vision_data)
        
        # Format Opportunities tab  
        opportunities = DashboardFormatter._format_opportunities(synthesis_data, vision_data)
        
        # Format Market Overview
        market_overview = DashboardFormatter._format_market_overview(recon_data, vision_data)
        
        # Extract video selection data
        video_selection = vision_data.get("video_selection", {})
        
        return DashboardData(
            query=query,
            region=region,
            analysis_date=datetime.utcnow().isoformat() + "Z",
            run_id=run_id,
            competition_analysis=competition_analysis,
            trending_content=trending_content,
            opportunities=opportunities,
            market_overview=market_overview,
            video_selection=video_selection
        )
    
    @staticmethod
    def _load_artifact(file_path: Path) -> Dict[str, Any]:
        """Load artifact JSON file."""
        try:
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}
    
    @staticmethod
    def _format_competition_analysis(recon_data: Dict[str, Any], vision_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format data for Competition Analysis dashboard tab."""
        
        items = recon_data.get("items", [])
        
        # Extract channel performance data
        channels = {}
        for item in items:
            channel_id = item.get("channel_id")
            channel_title = item.get("channel_title")
            
            if channel_id and channel_title:
                stats = item.get("stats", {})
                
                if channel_id not in channels:
                    channels[channel_id] = {
                        "channel_id": channel_id,
                        "channel_title": channel_title,
                        "video_count": 0,
                        "total_views": 0,
                        "total_likes": 0,
                        "total_comments": 0,
                        "avg_views": 0,
                        "avg_engagement_rate": 0,
                        "performance_tier": "standard"
                    }
                
                channel = channels[channel_id]
                channel["video_count"] += 1
                channel["total_views"] += stats.get("view_count", 0)
                channel["total_likes"] += stats.get("like_count", 0)
                channel["total_comments"] += stats.get("comment_count", 0)
        
        # Calculate averages and performance tiers
        competitors = []
        all_avg_views = []
        
        for channel in channels.values():
            if channel["video_count"] > 0:
                channel["avg_views"] = channel["total_views"] // channel["video_count"]
                channel["avg_engagement_rate"] = channel["total_likes"] / max(channel["total_views"], 1)
                all_avg_views.append(channel["avg_views"])
        
        # Determine performance benchmarks
        if all_avg_views:
            avg_benchmark = sum(all_avg_views) / len(all_avg_views)
            top_benchmark = avg_benchmark * 2
            
            # Classify channels and identify breakouts
            for channel in channels.values():
                if channel["avg_views"] > top_benchmark:
                    channel["performance_tier"] = "breakout"
                elif channel["avg_views"] > avg_benchmark:
                    channel["performance_tier"] = "above_average"
                else:
                    channel["performance_tier"] = "standard"
                
                competitors.append(channel)
        
        # Sort by average views (best performers first)
        competitors.sort(key=lambda x: x["avg_views"], reverse=True)
        
        # Calculate niche averages for benchmarking
        total_videos = len(items)
        total_views = sum(item.get("stats", {}).get("view_count", 0) for item in items)
        total_likes = sum(item.get("stats", {}).get("like_count", 0) for item in items)
        
        niche_averages = {
            "avg_views_per_video": total_views // max(total_videos, 1),
            "avg_engagement_rate": total_likes / max(total_views, 1),
            "total_competitors": len(competitors),
            "breakout_channels": len([c for c in competitors if c["performance_tier"] == "breakout"])
        }
        
        return {
            "competitors": competitors,
            "niche_averages": niche_averages,
            "performance_benchmarks": {
                "standard_threshold": avg_benchmark if all_avg_views else 0,
                "breakout_threshold": top_benchmark if all_avg_views else 0
            },
            "updated_at": datetime.utcnow().isoformat() + "Z"
        }
    
    @staticmethod
    def _format_trending_content(recon_data: Dict[str, Any], vision_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format data for Trending Content dashboard tab."""
        
        items = recon_data.get("items", [])
        
        # Sort by recency and performance to identify trending content
        trending_videos = []
        for item in items:
            stats = item.get("stats", {})
            view_count = stats.get("view_count", 0)
            like_count = stats.get("like_count", 0)
            
            # Calculate velocity score (views per day since publish)
            try:
                pub_date = datetime.fromisoformat(item.get("published_at", "").replace('Z', '+00:00'))
                days_since_publish = max((datetime.utcnow().replace(tzinfo=pub_date.tzinfo) - pub_date).days, 1)
                velocity_score = view_count / days_since_publish
            except:
                velocity_score = view_count / 30  # Fallback to 30-day estimate
            
            trending_video = {
                "video_id": item.get("video_id", ""),
                "title": item.get("title", ""),
                "channel_title": item.get("channel_title", ""),
                "thumbnail_url": item.get("thumbnail_url", ""),
                "published_at": item.get("published_at", ""),
                "url": f"https://www.youtube.com/watch?v={item.get('video_id', '')}" if item.get('video_id') else "",
                "metrics": {
                    "view_count": view_count,
                    "like_count": like_count,
                    "comment_count": stats.get("comment_count", 0),
                    "engagement_rate": like_count / max(view_count, 1),
                    "velocity_score": round(velocity_score, 0)
                },
                "trend_indicators": {
                    "high_velocity": velocity_score > 1000,
                    "recent_publish": days_since_publish <= 7,
                    "viral_potential": view_count > 100000 and days_since_publish <= 30
                }
            }
            
            trending_videos.append(trending_video)
        
        # Sort by velocity score (trending strength)
        trending_videos.sort(key=lambda x: x["metrics"]["velocity_score"], reverse=True)
        
        # Identify trend patterns from Google Trends data
        trends_data = recon_data.get("trends_data", {})
        trend_momentum = DashboardFormatter._analyze_trend_momentum(trends_data)
        
        return {
            "trending_videos": trending_videos,
            "trend_momentum": trend_momentum,
            "trending_summary": {
                "total_videos": len(trending_videos),
                "high_velocity_count": sum(1 for v in trending_videos if v["trend_indicators"]["high_velocity"]),
                "recent_uploads": sum(1 for v in trending_videos if v["trend_indicators"]["recent_publish"]),
                "viral_candidates": sum(1 for v in trending_videos if v["trend_indicators"]["viral_potential"])
            },
            "updated_at": datetime.utcnow().isoformat() + "Z"
        }
    
    @staticmethod
    def _format_opportunities(synthesis_data: Dict[str, Any], vision_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format data for Opportunities dashboard tab."""
        
        # Extract strategic insights
        recommendations = synthesis_data.get("recommendations", [])
        summary = synthesis_data.get("summary", "")
        
        # Extract market analysis scores
        vision_analysis = vision_data.get("vision_analysis", {})
        market_analysis = vision_analysis.get("market_analysis", {})
        faceless_content = vision_analysis.get("faceless_content", {})
        
        market_staleness_index = market_analysis.get("market_staleness_index", 50.0)
        faceless_viability_score = faceless_content.get("faceless_viability_score", 0.0)
        
        # Calculate overall opportunity score
        opportunity_score = DashboardFormatter._calculate_opportunity_score(
            market_staleness_index, faceless_viability_score, len(recommendations)
        )
        
        # Extract transcript insights
        transcript_analysis = vision_data.get("transcript_analysis", {})
        content_themes = transcript_analysis.get("content_themes", [])
        successful_hooks = transcript_analysis.get("successful_hooks", [])
        content_gaps = transcript_analysis.get("content_gaps", [])
        
        # Create optimal content blueprint
        content_blueprint = {
            "recommended_format": "personality-driven" if faceless_viability_score < 50 else "faceless-friendly",
            "content_themes": content_themes[:5],  # Top 5 themes
            "proven_hooks": successful_hooks[:3],  # Top 3 hooks
            "content_gaps": content_gaps[:3] if content_gaps else ["Market analysis needed"],
            "opportunity_level": DashboardFormatter._get_opportunity_level(opportunity_score)
        }
        
        # Format sub-niches and promising areas
        sub_niches = DashboardFormatter._identify_sub_niches(transcript_analysis, recommendations)
        
        return {
            "opportunity_summary": {
                "overall_score": round(opportunity_score, 1),
                "opportunity_level": content_blueprint["opportunity_level"],
                "market_freshness": 100 - market_staleness_index,  # Inverse of staleness
                "faceless_viability": faceless_viability_score,
                "content_volume": len(recommendations)
            },
            "optimal_content_blueprint": content_blueprint,
            "strategic_recommendations": recommendations[:10],  # Top 10 recommendations
            "sub_niches": sub_niches,
            "market_insights": {
                "summary": summary,
                "key_themes": content_themes,
                "market_saturation": "high" if market_staleness_index > 70 else "moderate" if market_staleness_index > 40 else "low"
            },
            "updated_at": datetime.utcnow().isoformat() + "Z"
        }
    
    @staticmethod
    def _format_market_overview(recon_data: Dict[str, Any], vision_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format general market overview data."""
        
        items = recon_data.get("items", [])
        trends_data = recon_data.get("trends_data", {})
        
        # Calculate market metrics
        total_videos = len(items)
        total_views = sum(item.get("stats", {}).get("view_count", 0) for item in items)
        avg_views = total_views // max(total_videos, 1)
        
        # Extract key scores
        vision_analysis = vision_data.get("vision_analysis", {})
        market_staleness = vision_analysis.get("market_analysis", {}).get("market_staleness_index", 50.0)
        faceless_viability = vision_analysis.get("faceless_content", {}).get("faceless_viability_score", 0.0)
        
        # Calculate trend velocity
        trend_velocity = trends_data.get("trend_velocity", 0.0) if trends_data.get("metadata", {}).get("available") else None
        
        return {
            "market_size": {
                "video_sample_size": total_videos,
                "total_sample_views": total_views,
                "average_views_per_video": avg_views
            },
            "market_health": {
                "staleness_index": market_staleness,
                "freshness_score": 100 - market_staleness,
                "trend_velocity": trend_velocity,
                "faceless_viability": faceless_viability
            },
            "data_quality": {
                "transcript_coverage": vision_data.get("transcript_analysis", {}).get("transcript_availability", {}).get("transcript_coverage", 0.0),
                "trends_data_available": trends_data.get("metadata", {}).get("available", False),
                "vision_analysis_complete": bool(vision_analysis)
            }
        }
    
    @staticmethod
    def _analyze_trend_momentum(trends_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze Google Trends momentum."""
        if not trends_data.get("metadata", {}).get("available"):
            return {"status": "unavailable", "momentum": "unknown"}
        
        series = trends_data.get("interest_over_time", [])
        if len(series) < 3:
            return {"status": "insufficient_data", "momentum": "unknown"}
        
        # Calculate momentum
        recent_avg = sum(point.get("value", 0) for point in series[-3:]) / 3
        older_avg = sum(point.get("value", 0) for point in series[:3]) / 3
        
        if recent_avg > older_avg * 1.2:
            momentum = "accelerating"
        elif recent_avg < older_avg * 0.8:
            momentum = "declining"
        else:
            momentum = "stable"
        
        return {
            "status": "available",
            "momentum": momentum,
            "recent_interest": round(recent_avg, 1),
            "trend_velocity": trends_data.get("trend_velocity", 0.0),
            "related_queries": trends_data.get("related_queries", {"top": [], "rising": []})
        }
    
    @staticmethod
    def _calculate_opportunity_score(staleness: float, faceless_viability: float, rec_count: int) -> float:
        """Calculate overall opportunity score (0-100)."""
        
        # Market freshness (inverse of staleness) - 40 points
        freshness_score = (100 - staleness) * 0.4
        
        # Content viability - 30 points  
        viability_score = faceless_viability * 0.3
        
        # Content volume/depth - 30 points
        volume_score = min(rec_count * 3, 30.0)
        
        return min(freshness_score + viability_score + volume_score, 100.0)
    
    @staticmethod
    def _get_opportunity_level(score: float) -> str:
        """Get opportunity level based on score."""
        if score >= 80:
            return "high"
        elif score >= 60:
            return "moderate"
        elif score >= 40:
            return "low"
        else:
            return "limited"
    
    @staticmethod
    def _identify_sub_niches(transcript_analysis: Dict[str, Any], recommendations: List[str]) -> List[Dict[str, Any]]:
        """Identify promising sub-niches from content analysis."""
        
        themes = transcript_analysis.get("content_themes", [])
        keywords = transcript_analysis.get("actionable_keywords", [])
        
        # Create sub-niche opportunities
        sub_niches = []
        
        # From content themes
        for i, theme in enumerate(themes[:3]):
            sub_niches.append({
                "name": theme,
                "type": "content_theme",
                "opportunity_level": "high" if i == 0 else "moderate",
                "estimated_competition": "moderate",
                "recommended_approach": "in-depth analysis"
            })
        
        # From keywords
        for keyword in keywords[:2]:
            sub_niches.append({
                "name": f"Advanced {keyword}",
                "type": "keyword_opportunity", 
                "opportunity_level": "moderate",
                "estimated_competition": "low",
                "recommended_approach": "tutorial series"
            })
        
        return sub_niches[:5]  # Limit to top 5