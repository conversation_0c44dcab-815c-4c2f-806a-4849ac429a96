from __future__ import annotations

import io
import time
from dataclasses import dataclass
from typing import Dict, List, Optional
from urllib.parse import urlparse

# Optional imports guarded to keep this pass lightweight
try:
    import cv2  # type: ignore
    import numpy as np  # type: ignore
except Exception:  # pragma: no cover
    cv2 = None  # type: ignore
    np = None  # type: ignore

try:
    import requests
except Exception:  # pragma: no cover
    requests = None  # type: ignore


@dataclass(frozen=True)
class VisionConfig:
    download_timeout_sec: float = 5.0
    connect_timeout_sec: float = 3.0
    # Haar cascade path; if None, use OpenCV's default prepackaged cascade if available
    haarcascade_frontalface_default: Optional[str] = None


class VisionService:
    """
    Minimal face detection in thumbnails using OpenCV Haar cascade.

    Behavior:
    - Best-effort: returns a dict mapping URL -> face_count (int), or False if detection couldn't run.
    - Uses safe download timeouts to avoid long network stalls.
    - If OpenCV or requests are not available, returns False for each URL as a soft indication.

    TODO:
    - Replace Haar cascade with a more robust DNN-based detector when wiring full pipeline.
    - Add caching to avoid re-downloading the same thumbnail.
    """

    def __init__(self, config: Optional[VisionConfig] = None) -> None:
        self.config = config or VisionConfig()

        self._face_cascade = None
        if cv2 is not None:
            try:
                # Prefer explicit path from config; otherwise try to construct a reasonable default.
                cascade_path = self.config.haarcascade_frontalface_default
                if not cascade_path:
                    # Some OpenCV builds expose cv2.data.haarcascades; guard with getattr to avoid static errors.
                    cv2_data = getattr(cv2, "data", None)
                    haar_root = getattr(cv2_data, "haarcascades", None) if cv2_data is not None else None
                    if isinstance(haar_root, str) and len(haar_root) > 0:
                        cascade_path = (
                            haar_root
                            if haar_root.endswith("haarcascade_frontalface_default.xml")
                            else f"{haar_root}haarcascade_frontalface_default.xml"
                        )
                if cascade_path:
                    self._face_cascade = cv2.CascadeClassifier(cascade_path)
            except Exception:
                self._face_cascade = None

    def _download_image(self, url: str) -> Optional[bytes]:
        if requests is None:
            return None
        # Quick URL sanity
        try:
            parsed = urlparse(url)
            if parsed.scheme not in ("http", "https"):
                return None
        except Exception:
            return None

        try:
            resp = requests.get(
                url,
                timeout=(self.config.connect_timeout_sec, self.config.download_timeout_sec),
                stream=True,
            )
            if not resp.ok:
                return None
            # Limit read to a reasonable amount (thumbnails are small)
            content = resp.content
            # Simple guard against overly large responses
            if len(content) > 10 * 1024 * 1024:
                return None
            return content
        except Exception:
            return None

    def _detect_faces_bytes(self, data: bytes) -> Optional[int]:
        if cv2 is None or np is None or self._face_cascade is None:
            return None
        try:
            img_array = np.frombuffer(data, dtype=np.uint8)
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            if img is None:
                return None
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            faces = self._face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=3, minSize=(20, 20))
            return int(len(faces))
        except Exception:
            return None

    def detect_faces_in_thumbnails(self, urls: List[str]) -> Dict[str, int | bool]:
        """
        Returns dict { url: face_count | False }

        - face_count: integer number of detected faces when detection succeeded
        - False: used as a sentinel to indicate detection couldn't run (e.g., deps missing or download failed)
        """
        results: Dict[str, int | bool] = {}
        if not urls:
            return results

        for url in urls:
            try:
                data = self._download_image(url)
                if not data:
                    results[url] = False
                    continue
                count = self._detect_faces_bytes(data)
                results[url] = count if (isinstance(count, int)) else False
            except Exception:
                results[url] = False

        return results