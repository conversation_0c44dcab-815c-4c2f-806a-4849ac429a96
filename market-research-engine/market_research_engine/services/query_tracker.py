from __future__ import annotations

import json
import time
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple


@dataclass
class QueryHistory:
    query: str
    region: str
    run_id: str
    timestamp: str  # ISO format
    metrics: Dict[str, Any]
    trends_velocity: Optional[float] = None
    market_staleness: Optional[float] = None
    faceless_viability: Optional[float] = None


@dataclass
class QueryTracker:
    """
    Service for tracking queries over time to monitor market changes.
    Enables comparative analysis and trend monitoring.
    """
    
    def __init__(self, storage_dir: Path):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.history_file = self.storage_dir / "query_history.json"
    
    def _load_history(self) -> List[Dict[str, Any]]:
        """Load existing query history from storage."""
        try:
            if self.history_file.exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return []
    
    def _save_history(self, history: List[Dict[str, Any]]) -> None:
        """Save query history to storage."""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
        except Exception:
            pass  # Soft fail
    
    def track_query(
        self,
        query: str,
        region: str,
        run_id: str,
        artifacts_dir: Path
    ) -> None:
        """Track a new query execution with its results."""
        
        # Extract metrics from artifacts
        metrics = self._extract_metrics(artifacts_dir)
        
        # Create history entry
        entry = QueryHistory(
            query=query.strip().lower(),  # Normalize for comparison
            region=region.upper(),
            run_id=run_id,
            timestamp=datetime.utcnow().isoformat() + "Z",
            metrics=metrics,
            trends_velocity=metrics.get("trends_velocity"),
            market_staleness=metrics.get("market_staleness_index"),
            faceless_viability=metrics.get("faceless_viability_score")
        )
        
        # Load existing history and add new entry
        history = self._load_history()
        history.append(asdict(entry))
        
        # Keep only last 100 entries per query to prevent unbounded growth
        self._cleanup_history(history)
        
        # Save updated history
        self._save_history(history)
    
    def _extract_metrics(self, artifacts_dir: Path) -> Dict[str, Any]:
        """Extract key metrics from run artifacts."""
        metrics = {}
        
        try:
            # Extract from recon.json
            recon_path = artifacts_dir / "recon.json"
            if recon_path.exists():
                with open(recon_path, 'r', encoding='utf-8') as f:
                    recon_data = json.load(f)
                
                items = recon_data.get("items", [])
                metrics["video_count"] = len(items)
                
                # Calculate basic stats
                view_counts = [item.get("stats", {}).get("view_count", 0) for item in items]
                metrics["total_views"] = sum(view_counts)
                metrics["avg_views"] = sum(view_counts) / len(view_counts) if view_counts else 0
                
                # Extract trends velocity if available
                trends_data = recon_data.get("trends_data", {})
                if trends_data.get("trend_velocity"):
                    metrics["trends_velocity"] = trends_data["trend_velocity"]
            
            # Extract from vision.json
            vision_path = artifacts_dir / "vision.json"
            if vision_path.exists():
                with open(vision_path, 'r', encoding='utf-8') as f:
                    vision_data = json.load(f)
                
                vision_analysis = vision_data.get("vision_analysis", {})
                
                # Extract key scores
                market_analysis = vision_analysis.get("market_analysis", {})
                if market_analysis.get("market_staleness_index") is not None:
                    metrics["market_staleness_index"] = market_analysis["market_staleness_index"]
                
                faceless_content = vision_analysis.get("faceless_content", {})
                if faceless_content.get("faceless_viability_score") is not None:
                    metrics["faceless_viability_score"] = faceless_content["faceless_viability_score"]
                
                # Transcript coverage
                transcript_analysis = vision_data.get("transcript_analysis", {})
                transcript_availability = transcript_analysis.get("transcript_availability", {})
                if transcript_availability:
                    metrics["transcript_coverage"] = transcript_availability.get("transcript_coverage", 0.0)
        
        except Exception:
            pass  # Soft fail, return whatever metrics we collected
        
        metrics["extracted_at"] = datetime.utcnow().isoformat() + "Z"
        return metrics
    
    def _cleanup_history(self, history: List[Dict[str, Any]]) -> None:
        """Remove old entries to prevent unbounded growth."""
        # Group by query+region and keep only recent entries
        query_groups: Dict[Tuple[str, str], List[Dict[str, Any]]] = {}
        
        for entry in history:
            key = (entry.get("query", ""), entry.get("region", ""))
            if key not in query_groups:
                query_groups[key] = []
            query_groups[key].append(entry)
        
        # Keep only last 50 entries per query+region combo
        cleaned_history = []
        for group in query_groups.values():
            # Sort by timestamp and keep recent ones
            sorted_group = sorted(group, key=lambda x: x.get("timestamp", ""), reverse=True)
            cleaned_history.extend(sorted_group[:50])
        
        # Update the original list in place
        history.clear()
        history.extend(cleaned_history)
    
    def get_query_history(
        self, 
        query: str, 
        region: str = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get historical data for a specific query."""
        normalized_query = query.strip().lower()
        history = self._load_history()
        
        # Filter by query and optionally by region
        filtered = []
        for entry in history:
            if entry.get("query") == normalized_query:
                if region is None or entry.get("region") == region.upper():
                    filtered.append(entry)
        
        # Sort by timestamp (most recent first) and limit
        filtered.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        return filtered[:limit]
    
    def get_trending_queries(self, days: int = 7, limit: int = 10) -> List[Dict[str, Any]]:
        """Get queries that have been tracked recently with their trend info."""
        cutoff = (datetime.utcnow() - timedelta(days=days)).isoformat() + "Z"
        history = self._load_history()
        
        # Filter recent entries
        recent_entries = [
            entry for entry in history
            if entry.get("timestamp", "") >= cutoff
        ]
        
        # Group by query and get latest metrics for each
        query_latest: Dict[Tuple[str, str], Dict[str, Any]] = {}
        for entry in recent_entries:
            key = (entry.get("query", ""), entry.get("region", ""))
            existing = query_latest.get(key)
            
            if not existing or entry.get("timestamp", "") > existing.get("timestamp", ""):
                query_latest[key] = entry
        
        # Convert to list and sort by trend velocity or staleness
        trending = list(query_latest.values())
        trending.sort(
            key=lambda x: (
                x.get("trends_velocity", 0) * -1,  # High positive velocity first
                x.get("market_staleness", 100)     # Low staleness second
            )
        )
        
        return trending[:limit]
    
    def compare_query_runs(
        self, 
        query: str, 
        region: str = None
    ) -> Dict[str, Any]:
        """Compare recent runs of the same query to show market evolution."""
        history = self.get_query_history(query, region, limit=5)
        
        if len(history) < 2:
            return {"error": "Not enough historical data for comparison"}
        
        latest = history[0]
        previous = history[1]
        
        # Calculate changes
        changes = {}
        
        # Compare key metrics
        for metric in ["market_staleness_index", "faceless_viability_score", "avg_views", "transcript_coverage"]:
            latest_val = latest.get("metrics", {}).get(metric)
            previous_val = previous.get("metrics", {}).get(metric)
            
            if latest_val is not None and previous_val is not None:
                change = latest_val - previous_val
                change_pct = (change / max(abs(previous_val), 0.01)) * 100
                changes[metric] = {
                    "current": latest_val,
                    "previous": previous_val, 
                    "change": round(change, 2),
                    "change_percent": round(change_pct, 1)
                }
        
        return {
            "query": query,
            "region": region,
            "comparison_period": {
                "latest": latest.get("timestamp"),
                "previous": previous.get("timestamp")
            },
            "changes": changes,
            "total_runs": len(history)
        }