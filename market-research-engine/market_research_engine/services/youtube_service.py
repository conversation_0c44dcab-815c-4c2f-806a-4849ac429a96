from __future__ import annotations

import os
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import requests
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


YOUTUBE_API_KEY_ENV = "YOUTUBE_API_KEY"
DEFAULT_MAX_RESULTS = 30
DEFAULT_COMMENTS_MAX_THREADS = 100


class YouTubeServiceError(Exception):
    pass


@dataclass(frozen=True)
class YouTubeServiceConfig:
    api_key: Optional[str]


def _get_api_key() -> Optional[str]:
    # Reads API key from env; return None when absent (callers should handle gracefully).
    return os.getenv(YOUTUBE_API_KEY_ENV) or None


def _base_params() -> Dict[str, Any]:
    key = _get_api_key()
    return {"key": key} if key else {}


def _raise_on_bad_status(resp: requests.Response) -> None:
    if not resp.ok:
        try:
            detail = resp.json()
        except Exception:
            detail = {"text": resp.text}
        raise YouTubeServiceError(f"YouTube API error {resp.status_code}: {detail}")


class YouTubeService:
    """
    Minimal YouTube Data API v3 client.

    Notes:
    - API key provided via env YOUTUBE_API_KEY.
    - Tenacity retries on transient network/5xx errors.
    - TODOs left for full data wiring (pagination, error enrichment, partial failures).
    """

    BASE_URL = "https://www.googleapis.com/youtube/v3"

    def __init__(self, api_key: Optional[str] = None) -> None:
        self.config = YouTubeServiceConfig(api_key=api_key or _get_api_key())

    def _params_with_key(self, extra: Dict[str, Any]) -> Dict[str, Any]:
        params = dict(extra)
        if self.config.api_key:
            params["key"] = self.config.api_key
        return params

    @retry(
        reraise=True,
        retry=retry_if_exception_type((requests.exceptions.RequestException, YouTubeServiceError)),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=0.5, min=0.5, max=3),
    )
    def _get(self, path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        url = f"{self.BASE_URL}/{path}"
        resp = requests.get(url, params=self._params_with_key(params), timeout=15)
        _raise_on_bad_status(resp)
        return resp.json()

    # 1) search_videos
    def search_videos(self, query: str, max_results: int = DEFAULT_MAX_RESULTS) -> List[Dict[str, Any]]:
        """
        Search videos by query.

        Returns a list of items with id and snippet minimally.
        TODO: Handle pagination with nextPageToken when wiring full pipeline.
        """
        if not query or not query.strip():
            return []
        max_results = max(1, min(50, int(max_results)))
        try:
            data = self._get(
                "search",
                {
                    "part": "snippet",
                    "maxResults": max_results,
                    "q": query,
                    "type": "video",
                    "safeSearch": "none",
                    "order": "relevance",
                },
            )
            return data.get("items", []) or []
        except Exception:
            # Soft-fail for scaffolding
            return []

    # 2) get_video_details
    def get_video_details(self, video_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Fetch video details for provided IDs, including statistics.

        TODO: Split large ID lists into chunks of 50, merge results.
        """
        if not video_ids:
            return []
        ids_param = ",".join(video_ids[:50])
        try:
            data = self._get(
                "videos",
                {
                    "part": "snippet,contentDetails,statistics",
                    "id": ids_param,
                },
            )
            return data.get("items", []) or []
        except Exception:
            return []

    # 3) get_video_transcript
    def get_video_transcript(self, video_id: str) -> Optional[str]:
        """
        Placeholder for direct transcript retrieval if leveraging captions API in future.

        TODO: Wire to transcripts_service.get_transcript for composite behavior.
        """
        if not video_id:
            return None
        # Minimal placeholder
        return None

    # 4) get_channel_details
    def get_channel_details(self, channel_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Fetch channel details for provided IDs.

        TODO: Chunking and pagination handling when necessary.
        """
        if not channel_ids:
            return []
        ids_param = ",".join(channel_ids[:50])
        try:
            data = self._get(
                "channels",
                {
                    "part": "snippet,contentDetails,statistics,brandingSettings",
                    "id": ids_param,
                },
            )
            return data.get("items", []) or []
        except Exception:
            return []

    # 5) get_comment_threads
    def get_comment_threads(self, video_id: str, max_threads: int = DEFAULT_COMMENTS_MAX_THREADS, order: str = "relevance") -> List[Dict[str, Any]]:
        """
        Fetch comment threads for a video.

        TODO: Pagination via nextPageToken and respect max_threads.
        """
        if not video_id:
            return []
        order = order if order in ("time", "relevance") else "relevance"
        try:
            data = self._get(
                "commentThreads",
                {
                    "part": "snippet,replies",
                    "videoId": video_id,
                    "maxResults": min(100, max(1, int(max_threads))),
                    "order": order,
                    "textFormat": "plainText",
                },
            )
            return data.get("items", []) or []
        except Exception:
            return []


# TODO:
# - Add OAuth-based quota management if needed.
# - Add error code mapping for clearer soft warnings in pipeline.