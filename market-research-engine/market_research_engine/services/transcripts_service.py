from __future__ import annotations

from typing import Optional, List

# Optional dependencies guarded
try:
    from youtube_transcript_api import YouTubeTranscriptApi  # type: ignore
    from youtube_transcript_api._errors import (  # type: ignore
        TranscriptsDisabled,
        NoTranscriptFound,
        VideoUnavailable,
        CouldNotRetrieveTranscript,
    )
except Exception:  # pragma: no cover
    YouTubeTranscriptApi = None  # type: ignore
    TranscriptsDisabled = NoTranscriptFound = VideoUnavailable = CouldNotRetrieveTranscript = Exception  # type: ignore


class TranscriptsService:
    """
    Minimal transcripts retrieval service.

    Strategy:
    - Prefer YouTube transcript API (community/auto-generated) via youtube-transcript-api as a fallback path.
    - In a future pass, add direct YouTube Captions API usage where possible.
    - Return a single plain text string or None on failure.
    """

    def get_transcript(self, video_id: str) -> Optional[str]:
        """
        Attempt to fetch transcript lines for a given video id and collapse into text.
        Returns None on any error or if no transcript is available.
        """
        if not video_id:
            return None

        # TODO: First attempt: use official captions endpoint when a token is available.
        # Placeholder: directly fallback to youtube-transcript-api when installed.
        if YouTubeTranscriptApi is None:
            return None

        try:
            # Try retrieving transcript in English first; fallback to generated or other languages if needed.
            # youtube-transcript-api will choose best match when languages list is provided.
            languages: List[str] = ["en", "en-US", "en-GB"]
            transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=languages)
            text = " ".join([seg.get("text", "") for seg in transcript if seg.get("text")])
            return text.strip() or None
        except (TranscriptsDisabled, NoTranscriptFound, VideoUnavailable, CouldNotRetrieveTranscript, Exception):
            # Soft-fail for scaffolding; return None on any error
            return None