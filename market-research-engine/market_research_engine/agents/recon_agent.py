from __future__ import annotations

from typing import Any, Dict, List, Optional

# Guarded imports to avoid hard failures during scaffolding
try:
    from ..services.youtube_service import YouTubeService  # type: ignore
except Exception:  # pragma: no cover
    YouTubeService = None  # type: ignore

try:
    from ..services.trends_service import TrendsService  # type: ignore
except Exception:  # pragma: no cover
    TrendsService = None  # type: ignore


class ReconAgent:
    """
    Market Reconnaissance Agent (services-only V1)

    Responsibilities:
    - Search YouTube for top videos relevant to the query
    - Fetch video details and derive unique channels
    - Optionally limit number of channels (depth-dependent)
    - Fetch channel details
    - Fetch comment threads for top-N videos (by viewCount where available; fallback to order as returned)
    - Pull Google Trends snapshots (interest over time + related queries)
    """

    def __init__(self) -> None:
        self._yt = YouTubeService() if YouTubeService else None
        self._tr = TrendsService() if TrendsService else None

    def collect(
        self,
        query: str,
        region: str,
        limits: Dict[str, Optional[int]],
        skip_transcripts: bool = True,
        skip_vision: bool = True,
    ) -> Dict[str, Any]:
        max_videos = int(limits.get("max_videos") or 0) or 30
        max_channels = limits.get("max_channels")
        max_comments = int(limits.get("max_comments") or 0) or 100

        videos: List[Dict[str, Any]] = []
        channels: List[Dict[str, Any]] = []
        comments: List[Dict[str, Any]] = []
        trends: Dict[str, Any] = {"top": [], "rising": [], "series": []}

        if not self._yt or not query:
            return {"videos": videos, "channels": channels, "comments": comments, "trends": trends}

        # 1) Search videos
        search_items = self._yt.search_videos(query=query, max_results=max_videos)
        video_ids = [it.get("id", {}).get("videoId") for it in search_items if it.get("id", {}).get("videoId")]
        video_ids = [v for v in video_ids if isinstance(v, str)]

        # 2) Fetch video details
        video_details = self._yt.get_video_details(video_ids=video_ids) if video_ids else []
        videos = video_details

        # 3) Derive unique channel ids
        channel_ids: List[str] = []
        for v in video_details:
            cid = v.get("snippet", {}).get("channelId")
            if cid and cid not in channel_ids:
                channel_ids.append(cid)

        if max_channels is not None and max_channels >= 0:
            channel_ids = channel_ids[: max_channels]

        # 4) Fetch channel details
        channels = self._yt.get_channel_details(channel_ids=channel_ids) if channel_ids else []

        # 5) Pick top videos for comments (by viewCount when available)
        def _parse_int(x: Any) -> int:
            try:
                return int(x)
            except Exception:
                return 0

        sorted_for_comments: List[Dict[str, Any]] = sorted(
            video_details,
            key=lambda v: _parse_int(v.get("statistics", {}).get("viewCount")),
            reverse=True,
        )

        for v in sorted_for_comments[: min(len(sorted_for_comments), max(1, max_videos))]:
            vid = v.get("id")
            if not vid:
                continue
            cmts = self._yt.get_comment_threads(video_id=vid, max_threads=max_comments, order="relevance") or []
            # attach videoId to each top-level comment thread
            for c in cmts:
                c["__videoId"] = vid
            comments.extend(cmts)

        # 6) Trends snapshot (best-effort)
        if self._tr and query:
            try:
                series = self._tr.get_interest_over_time(query=query, geo=region, months=24)
                related = self._tr.get_related_queries(query=query, geo=region, top_limit=15, rising_limit=15)
                trends = {"series": series or [], "top": related.get("top", []), "rising": related.get("rising", [])}
            except Exception:
                # keep defaults
                pass

        return {
            "videos": videos,
            "channels": channels,
            "comments": comments,
            "trends": trends,
            "notes": {
                "skip_transcripts": skip_transcripts,
                "skip_vision": skip_vision,
            },
        }