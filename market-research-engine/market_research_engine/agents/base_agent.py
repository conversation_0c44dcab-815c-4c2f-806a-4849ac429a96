from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict, Optional


@dataclass
class AgentContext:
    """Lightweight context object for agents."""
    project: str
    params: Dict[str, Any]


class BaseAgent:
    """Placeholder base class for CrewAI-style agents."""

    name: str = "base-agent"
    description: str = "Base agent placeholder"

    def __init__(self, context: AgentContext) -> None:
        self.context = context

    async def run(self) -> Dict[str, Any]:
        """Execute the agent task. Placeholder implementation."""
        return {
            "agent": self.name,
            "project": self.context.project,
            "params": self.context.params,
            "status": "noop",
        }