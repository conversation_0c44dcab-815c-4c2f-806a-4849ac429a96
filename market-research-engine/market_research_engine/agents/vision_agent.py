from __future__ import annotations

from typing import Any, Dict, List, Optional

# Guarded imports to avoid hard failures during scaffolding
try:
    from ..services.vision_service import VisionService  # type: ignore
except Exception:  # pragma: no cover
    VisionService = None  # type: ignore

try:
    from ..services.youtube_service import YouTubeService  # type: ignore
except Exception:  # pragma: no cover
    YouTubeService = None  # type: ignore


class VisionAgent:
    """
    Vision/Transcription Analysis Agent (services-only V1)

    Responsibilities:
    - Extract and analyze video thumbnails for face detection
    - Fetch and process video transcripts/captions (future enhancement)
    - Calculate faceless viability scores based on visual content
    - Provide enhanced insights for the synthesis phase
    
    This agent processes the visual and textual content of videos to provide
    deeper insights that complement the quantitative analysis from other agents.
    """

    def __init__(self) -> None:
        self._vision = VisionService() if VisionService else None
        self._yt = YouTubeService() if YouTubeService else None

    def analyze_visual_content(
        self,
        recon_data: Dict[str, Any],
        analysis_data: Dict[str, Any],
        skip_vision: bool = False,
        skip_transcripts: bool = True,  # Default to True until transcript processing is implemented
    ) -> Dict[str, Any]:
        """
        Analyze visual content from reconnaissance and analysis data.
        
        Args:
            recon_data: Data from ReconAgent containing videos, channels, etc.
            analysis_data: Data from AnalystAgent containing aggregated metrics
            skip_vision: Skip face detection analysis
            skip_transcripts: Skip transcript analysis (placeholder for future)
            
        Returns:
            Dict containing vision analysis results
        """
        videos = recon_data.get("videos", [])
        top_videos = analysis_data.get("top_videos", [])
        
        result = {
            "vision_analysis": {
                "total_videos_analyzed": 0,
                "face_detection_results": {},
                "faceless_videos_count": 0,
                "faceless_videos_ratio": 0.0,
                "top_faceless_videos": [],
            },
            "transcript_analysis": {
                "total_transcripts_processed": 0,
                "transcript_availability": {},
                # Future: keyword extraction, sentiment analysis, etc.
            },
            "viability_scores": {
                "faceless_viability_score": 50.0,  # Default neutral score
                "content_accessibility_score": 50.0,
            },
            "notes": {
                "skip_vision": skip_vision,
                "skip_transcripts": skip_transcripts,
                "vision_service_available": self._vision is not None,
                "youtube_service_available": self._yt is not None,
            }
        }

        if skip_vision or not self._vision or not videos:
            return result

        # Extract thumbnail URLs from videos
        thumbnail_urls = []
        video_thumbnail_map = {}
        
        for video in videos:
            video_id = video.get("id")
            if not video_id:
                continue
                
            # YouTube API typically provides multiple thumbnail sizes
            thumbnails = video.get("snippet", {}).get("thumbnails", {})
            
            # Prefer higher quality thumbnails: maxres > high > medium > default
            for quality in ["maxres", "high", "medium", "default"]:
                if quality in thumbnails:
                    url = thumbnails[quality].get("url")
                    if url:
                        thumbnail_urls.append(url)
                        video_thumbnail_map[url] = video_id
                        break

        if not thumbnail_urls:
            return result

        # Perform face detection
        try:
            face_results = self._vision.detect_faces_in_thumbnails(thumbnail_urls)
            result["vision_analysis"]["face_detection_results"] = face_results
            result["vision_analysis"]["total_videos_analyzed"] = len(thumbnail_urls)
            
            # Calculate faceless metrics
            faceless_count = 0
            faceless_videos = []
            
            for url, face_count in face_results.items():
                video_id = video_thumbnail_map.get(url)
                if video_id and isinstance(face_count, int) and face_count == 0:
                    faceless_count += 1
                    # Find the corresponding video data
                    for video in videos:
                        if video.get("id") == video_id:
                            faceless_videos.append({
                                "video_id": video_id,
                                "title": video.get("snippet", {}).get("title", ""),
                                "views": int(video.get("statistics", {}).get("viewCount", 0)),
                                "likes": int(video.get("statistics", {}).get("likeCount", 0)),
                                "channel_id": video.get("snippet", {}).get("channelId", ""),
                            })
                            break
            
            # Sort faceless videos by views (descending)
            faceless_videos.sort(key=lambda x: x["views"], reverse=True)
            
            result["vision_analysis"]["faceless_videos_count"] = faceless_count
            result["vision_analysis"]["faceless_videos_ratio"] = (
                faceless_count / len(thumbnail_urls) if thumbnail_urls else 0.0
            )
            result["vision_analysis"]["top_faceless_videos"] = faceless_videos[:10]
            
            # Calculate faceless viability score
            # Higher ratio of successful faceless videos with good performance = higher score
            faceless_viability = self._calculate_faceless_viability_score(
                faceless_videos, top_videos, result["vision_analysis"]["faceless_videos_ratio"]
            )
            result["viability_scores"]["faceless_viability_score"] = faceless_viability
            
        except Exception as e:
            result["notes"]["vision_analysis_error"] = str(e)

        # Placeholder for transcript analysis (future enhancement)
        if not skip_transcripts and self._yt:
            result = self._analyze_transcripts(result, videos)

        return result

    def _calculate_faceless_viability_score(
        self, 
        faceless_videos: List[Dict[str, Any]], 
        top_videos: List[Dict[str, Any]], 
        faceless_ratio: float
    ) -> float:
        """
        Calculate faceless content viability score (0-100).
        
        Factors:
        - Ratio of faceless videos in dataset
        - Performance of faceless videos vs overall top performers
        - View engagement correlation for faceless content
        """
        if not faceless_videos:
            return 0.0
        
        # Base score from faceless ratio (0-40 points)
        ratio_score = min(faceless_ratio * 100, 40.0)
        
        # Performance score: how well do faceless videos perform? (0-40 points)
        performance_score = 0.0
        if top_videos and faceless_videos:
            # Count how many top videos are faceless
            top_video_ids = {v.get("id") for v in top_videos[:10]}
            faceless_in_top = sum(1 for v in faceless_videos[:10] if v["video_id"] in top_video_ids)
            performance_score = min((faceless_in_top / min(10, len(top_videos))) * 40.0, 40.0)
        
        # Engagement quality score for faceless content (0-20 points)
        engagement_score = 0.0
        if faceless_videos:
            total_engagement_ratio = 0.0
            valid_videos = 0
            
            for video in faceless_videos[:10]:
                views = video["views"]
                likes = video["likes"]
                if views > 0:
                    engagement_ratio = likes / views
                    total_engagement_ratio += engagement_ratio
                    valid_videos += 1
            
            if valid_videos > 0:
                avg_engagement = total_engagement_ratio / valid_videos
                # Normalize engagement score (typical good engagement is around 0.01-0.05)
                engagement_score = min(avg_engagement * 1000, 20.0)
        
        total_score = ratio_score + performance_score + engagement_score
        return min(total_score, 100.0)

    def _analyze_transcripts(self, result: Dict[str, Any], videos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Placeholder for transcript analysis functionality.
        
        Future enhancements:
        - Fetch video captions/transcripts
        - Extract keywords and themes
        - Analyze sentiment and tone
        - Identify successful content patterns
        """
        # TODO: Implement transcript fetching and analysis
        result["transcript_analysis"]["total_transcripts_processed"] = 0
        result["transcript_analysis"]["transcript_availability"] = {}
        result["notes"]["transcript_analysis_status"] = "Not yet implemented"
        
        return result