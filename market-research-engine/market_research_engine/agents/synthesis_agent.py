from __future__ import annotations

from typing import Any, Dict, List


class SynthesisAgent:
    """
    Strategic Synthesis Expert (services-only V1)

    Produces a concise markdown Final_Market_Report.md using the quantitative analysis
    and reconnaissance artifacts. This version is deterministic and template-driven
    (no LLM), suitable for synchronous pipeline execution.
    """

    def summarize(self, analysis: Dict[str, Any], recon: Dict[str, Any], depth: str) -> str:
        aggr = analysis.get("aggregates", {}) or {}
        top_videos = analysis.get("top_videos", []) or []
        channels = analysis.get("channels", []) or []
        trends_summary = analysis.get("trends_summary", {}) or {}
        notes = analysis.get("notes", {}) or {}

        # Basic indices (heuristic placeholders for V1)
        total_views = int(aggr.get("total_views", 0) or 0)
        engagement_per_view = float(aggr.get("engagement_per_view", 0.0) or 0.0)
        series_points = int(trends_summary.get("series_points", 0) or 0)
        rising_count = int(trends_summary.get("rising_count", 0) or 0)

        # Niche Opportunity Score (0-100): weighted combo of total views and engagement_per_view
        opportunity_score = self._clamp_0_100(
            (min(total_views, 50_000_000) / 50_000_000.0) * 60.0
            + min(engagement_per_view, 0.05) / 0.05 * 40.0
        )

        # Market Staleness Index (0-100, higher is more stale): fewer rising queries and low series points -> staler
        staleness_index = self._clamp_0_100(
            (1.0 - min(rising_count, 20) / 20.0) * 60.0 + (1.0 - min(series_points, 100) / 100.0) * 40.0
        )

        # Faceless Viability Score (placeholder V1; vision pipeline not wired)
        # Assume neutral 50; in future, derive from thumbnail face detection and performance mapping.
        faceless_viability = 50

        # Compose markdown
        lines: List[str] = []
        lines.append("# Final Market Report")
        lines.append("")
        lines.append(f"Mode: {depth}")
        lines.append("")
        lines.append("## Executive Summary")
        lines.append(
            f"- Niche Opportunity Score: {opportunity_score:.1f}/100"
        )
        lines.append(
            f"- Market Staleness Index: {staleness_index:.1f}/100 (higher means more stale)"
        )
        lines.append(
            f"- Faceless Viability Score: {faceless_viability:.1f}/100 (placeholder V1)"
        )
        lines.append("")

        lines.append("## Quantitative Highlights")
        lines.append(f"- Total Videos Analyzed: {int(aggr.get('total_videos', 0) or 0)}")
        lines.append(f"- Total Views (sum): {total_views:,}")
        lines.append(f"- Engagement per View (≈ (likes + comments) / views): {engagement_per_view:.6f}")
        lines.append(f"- Total Comment Threads Fetched: {int(aggr.get('total_comment_threads', 0) or 0)}")
        lines.append(f"- Trends: series_points={series_points}, rising_count={rising_count}, top_count={int(trends_summary.get('top_count', 0) or 0)}")
        lines.append("")

        if top_videos:
            lines.append("## Top Videos (by Views)")
            lines.append("")
            lines.append("| Rank | Video ID | Title | Views | Likes | Comments | Channel | Published |")
            lines.append("|---:|---|---|---:|---:|---:|---|---|")
            for i, v in enumerate(top_videos[:10], start=1):
                vid = str(v.get("id") or "")
                title = str(v.get("title") or "").replace("|", "\\|")
                views = int(v.get("views") or 0)
                likes = int(v.get("likes") or 0)
                comms = int(v.get("comments") or 0)
                ch = str(v.get("channelId") or "")
                pub = str(v.get("publishedAt") or "")
                lines.append(f"| {i} | {vid} | {title} | {views:,} | {likes:,} | {comms:,} | {ch} | {pub} |")
            lines.append("")

        if channels:
            lines.append("## Channel Snapshot")
            lines.append("")
            lines.append("| Channel | Subscribers | Video Count | Total Views | Cadence Heuristic |")
            lines.append("|---|---:|---:|---:|---:|")
            for c in channels[:10]:
                title = str(c.get("title") or "").replace("|", "\\|")
                subs = int(c.get("subscribers") or 0)
                vcount = int(c.get("videoCount") or 0)
                vtotal = int(c.get("viewCount") or 0)
                cadence = float(c.get("cadence_heuristic") or 0.0)
                lines.append(f"| {title} | {subs:,} | {vcount:,} | {vtotal:,} | {cadence:.2f} |")
            lines.append("")

        lines.append("## Opportunity Signals")
        lines.append("- Strong views indicate baseline demand when Opportunity Score is elevated.")
        lines.append("- Rising queries suggest breakout sub-niches or seasonal spikes.")
        lines.append("- Higher engagement-per-view implies content formats that compel interaction.")
        lines.append("")
        lines.append("## Risks and Considerations")
        lines.append("- A high staleness index may indicate saturated or declining interest areas.")
        lines.append("- Metric noise: single-channel outliers can skew aggregate trends; corroborate with manual checks.")
        lines.append("- API availability and quotas may limit dataset completeness in V1.")
        lines.append("")
        lines.append("## Recommended Next Steps")
        lines.append("- Drill into rising queries with topic clustering and keyword variants.")
        lines.append("- Map top formats to production requirements; test a concept slate.")
        lines.append("- Re-run with transcripts/vision enabled once wired to refine viability signals.")
        lines.append("")
        lines.append("## Method Notes")
        lines.append(f"- Services-only V1; deterministic; no LLM generation.")
        lines.append(f"- Heuristic indices are approximations for quick triage.")
        lines.append(f"- Source artifacts: videos.json, channels.json, comments.json, trends.json, QuantitativeMarketAnalysis.json.")
        lines.append("")

        return "\n".join(lines)

    @staticmethod
    def _clamp_0_100(x: float) -> float:
        return 0.0 if x < 0.0 else 100.0 if x > 100.0 else x