from __future__ import annotations

from typing import Any, Dict, List, TypedDict


class TopVideo(TypedDict):
    id: str | None
    title: str | None
    channelId: str | None
    views: int
    likes: int
    comments: int
    publishedAt: str | None


class ChannelSnapshot(TypedDict, total=False):
    id: str | None
    title: str | None
    subscribers: int
    videoCount: int
    viewCount: int
    cadence_heuristic: float


class AnalystAgent:
    """
    Quantitative Market Analyst (services-only V1)

    Produces a simple quantitative snapshot:
    - aggregates: totals, top videos by viewCount
    - engagement: basic engagement-per-view ratios when likes/comments available
    - channels: subscriber counts and a rough 'cadence' proxy from available data (placeholder)
    """

    def analyze(self, recon: Dict[str, Any]) -> Dict[str, Any]:
        videos: List[Dict[str, Any]] = recon.get("videos", []) or []
        channels: List[Dict[str, Any]] = recon.get("channels", []) or []
        comments: List[Dict[str, Any]] = recon.get("comments", []) or []
        trends: Dict[str, Any] = recon.get("trends", {}) or {}

        def _to_int(val: Any) -> int:
            try:
                return int(val)
            except Exception:
                return 0

        # Aggregate video metrics
        total_videos = len(videos)
        total_views = sum(_to_int(v.get("statistics", {}).get("viewCount")) for v in videos)
        total_likes = sum(_to_int(v.get("statistics", {}).get("likeCount")) for v in videos)
        total_comments_count = sum(_to_int(v.get("statistics", {}).get("commentCount")) for v in videos)

        # Top videos by view count
        top_video_list: List[TopVideo] = []
        for v in videos:
            top_video_list.append(
                TopVideo(
                    id=v.get("id"),
                    title=(v.get("snippet", {}) or {}).get("title"),
                    channelId=(v.get("snippet", {}) or {}).get("channelId"),
                    views=_to_int((v.get("statistics", {}) or {}).get("viewCount")),
                    likes=_to_int((v.get("statistics", {}) or {}).get("likeCount")),
                    comments=_to_int((v.get("statistics", {}) or {}).get("commentCount")),
                    publishedAt=(v.get("snippet", {}) or {}).get("publishedAt"),
                )
            )
        top_video_list.sort(key=lambda x: x["views"])  # type: ignore[arg-type]
        top_video_list.reverse()
        top_videos: List[TopVideo] = top_video_list[:10]

        # Engagement per view (rudimentary)
        engagement_per_view = 0.0
        if total_views > 0:
            # combine likes and video-level comments
            engagement_per_view = (total_likes + total_comments_count) / float(total_views)

        # Channels snapshot
        channel_snapshots: List[ChannelSnapshot] = []
        for ch in channels:
            ch_stat = ch.get("statistics", {}) or {}
            ch_snip = ch.get("snippet", {}) or {}
            snap: ChannelSnapshot = ChannelSnapshot(
                id=ch.get("id"),
                title=ch_snip.get("title"),
                subscribers=_to_int(ch_stat.get("subscriberCount")),
                videoCount=_to_int(ch_stat.get("videoCount")),
                viewCount=_to_int(ch_stat.get("viewCount")),
            )
            channel_snapshots.append(snap)

        # Very rough "cadence" heuristic (placeholder): videoCount / (1 + subscribers/1000)
        for c in channel_snapshots:
            denom = 1.0 + (c.get("subscribers", 0) / 1000.0 if c.get("subscribers", 0) else 0.0)
            cadence = c.get("videoCount", 0) / denom if denom > 0 else c.get("videoCount", 0)
            c["cadence_heuristic"] = round(float(cadence), 2)

        # Trends digestion (keep as-pass)
        trends_summary = {
            "series_points": len(trends.get("series", []) or []),
            "top_count": len(trends.get("top", []) or []),
            "rising_count": len(trends.get("rising", []) or []),
        }

        # Comments count
        total_comment_threads = len(comments)

        return {
            "aggregates": {
                "total_videos": total_videos,
                "total_views": total_views,
                "total_likes": total_likes,
                "total_video_level_comments": total_comments_count,
                "engagement_per_view": engagement_per_view,
                "total_comment_threads": total_comment_threads,
            },
            "top_videos": top_videos,
            "channels": channel_snapshots,
            "trends_summary": trends_summary,
            "notes": {
                "method": "services-only v1; metrics are approximate and for triage",
            },
        }