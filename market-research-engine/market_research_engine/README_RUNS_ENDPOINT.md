Usage guide for POST /runs

Endpoint
POST /runs

Request body (JSON)
{
  "project": "string (required, will be sanitized to [A-Za-z0-9_-])",
  "params": {
    "query": "string (required) — topic or niche to research",
    "region": "string (optional) — Google Trends geo code like 'US', 'US-NY', 'CA-ON'"
  },
  "metadata": { "requested_by": "me" }   // optional
}

Successful response
{
  "run_id": "YYYY-MM-DD_HHMMSSZ-xxxxxxxxxx",
  "path": "runs/<run_id>",
  "created_at": "ISO-8601 UTC timestamp"
}

Artifacts created (placeholders initially)
- videos.json, channels.json, comments.json, trends.json
- QuantitativeMarketAnalysis.json
- Final_Market_Report.md
- status.json (run status lifecycle and region info)

status.json format
{
  "state": "initializing|collecting|analyzing|synthesizing|completed|failed",
  "started_at": "ISO-8601 UTC",
  "updated_at": "ISO-8601 UTC",
  "region_used": "string | null",
  "fallback_applied": true|false,
  "metadata_status_warning": "string | null",
  "status_note": "string | null",
  "error": "string | null"
}

Behavior
- Validates "project" is non-empty; sanitizes to alphanumerics, hyphen, underscore.
- Creates a timestamped run directory under RUNS_DIR (default ./runs, configurable via .env)
- Writes metadata.json in the run directory with project, params, metadata, environment.
- Initializes status.json with state "initializing" and planned region (params.region if provided).
- Performs a light Google Trends geo sanity check. If provided region fails, it falls back to US and sets:
  - region_used = "US"
  - fallback_applied = true
  - metadata_status_warning with a soft warning reason.
- Seeds artifact placeholders.
- For this pass, the endpoint completes quickly and sets state to "completed".

Configuration
- Copy .env.sample to .env and adjust as needed:
  APP_PORT=8000
  RUNS_DIR=./runs

Run locally
- With uvicorn:
  uvicorn market_research_engine.app:app --reload --port 8000

- With CLI:
  python -m market_research_engine serve --port 8000 --reload

Test example
curl -s http://localhost:8001/runs -H "Content-Type: application/json" -d '{
  "project": "demo",
  "params": { "query": "test niche", "region": "US-NY" }
}'

After running, inspect runs/<run_id>/status.json for:
- region_used set (may be US if fallback applied)
- state at least "completed" in this scaffold pass

Notes
- This is an initial scaffold. CrewAI integration placeholders exist under market_research_engine/agents and tools/.
- Structured logging via loguru; install dependencies with pip/uv/poetry based on pyproject.toml.