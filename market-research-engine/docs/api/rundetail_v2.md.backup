# RunDetail v2 Schema

Schema version: 2

Overview
RunDetail v2 is the canonical, versioned response for GET /runs/{run_id} and GET /runs/latest. It guarantees the presence of schema_version=2 and exposes execution hydration derived from the durable status.json. The schema is backward-compatible for legacy runs: when status.json is missing or malformed, execution is defaulted safely and the API still returns schema_version=2 to enable consistent client handling.

Endpoints
- GET /runs/{run_id}
- GET /runs/latest

Top-level fields
- run_id: string (required)
  Unique identifier for the run directory under runs/.

- path: string (required)
  Absolute filesystem path to the run directory.

- created_at: string (RFC3339, required)
  ISO-8601 timestamp for when the run directory was created (from metadata.json).

- artifacts: ArtifactItem[] (required; can be empty)
  A list of materialized artifacts available for the run.

- schema_version: integer (required)
  Fixed value of 2 for the v2 schema. All responses from this API contract return 2 to allow clients to branch logic if needed.

- execution: ExecutionMeta | null (required)
  Hydrated execution metadata. Will be present in typical cases; can be populated with defaults when status.json is missing or malformed.

ArtifactItem
- name: string (required)
  The file name relative to the run directory. If nested under artifacts/, the relative path includes the prefix (e.g. artifacts/report.md).

- size: integer (required)
  File size in bytes.

- modified_at: string (RFC3339, required)
  ISO-8601 timestamp of last modification time (from filesystem mtime).

- url: string (required)
  Direct URL to retrieve the artifact via GET /runs/{run_id}/artifacts/{name}.

ExecutionMeta
Hydrated from status.json when available.

- status: enum (required)
  One of: "queued", "running", "succeeded", "failed", "cancelled", "unknown"
  Mapped from status.json.state. If missing or malformed, "unknown" is used.

- started_at: string (RFC3339) | null (optional; default null)
  From status.json.started_at.

- ended_at: string (RFC3339) | null (optional; default null)
  From status.json.ended_at.

- duration_ms: integer | null (optional; default null)
  From status.json.duration_ms. Calculated by the state machine upon terminal transitions; can be null.

- error_type: string | null (optional; default null)
  Derived from status.json.error by splitting before the first ":" (e.g., "PipelineError" from "PipelineError:Boom").

- error_message: string | null (optional; default null)
  Mirror of status.json.error.

- cancelled_at: string (RFC3339) | null (optional; default null)
  From status.json.cancelled_at when a cancellation occurred.

- cancellation_reason: string | null (optional; default null)
  From status.json.cancellation_reason when a cancellation occurred.

- version: integer | null (optional; default null)
  From status.json.version if maintained. Acts as an optimistic concurrency and caching token for the UI.

Hydration Rules
- Source: status.json in the run directory is the source of truth for execution.
- Missing status.json: execution is present and defaults to:
  - status="unknown"
  - all timestamps null
  - duration_ms null
  - error fields null
  - version null

- Malformed status.json: same defaulting as above, without failing the request.

- Mapping:
  - execution.status = status.json.state (string) or "unknown" if missing.
  - started_at, ended_at, cancelled_at: strings (RFC3339) when present; otherwise null.
  - duration_ms: integer when present; otherwise null.
  - error_type: first token of error split by ":" or the entire string if no ":" present.
  - error_message: full error string.
  - cancellation_reason: direct mapping to string or null.
  - version: integer if present; null otherwise.

Backward compatibility
- Legacy runs missing schema_version in their original responses are now returned as schema_version=2. This enables a single client codepath to handle both legacy and new runs.
- If status.json is absent or partially written, the API responds with execution defaults instead of failing.

Versioning policy
- schema_version=2 is pinned for the RunDetail v2 contract.
- Future additive changes will maintain schema_version=2; breaking changes will bump schema_version.
- Clients SHOULD branch on schema_version to opt into newer fields or behaviors.

UI consumption guidance
- Use execution.status as the source of truth for lifecycle state.
- Use execution.version, when present, for client-side caching and optimistic concurrency (CAS). If not present, fallback to ETag or polling.
- Prefer ended_at and duration_ms for displaying timing when the run is terminal; for in-flight runs, started_at may be present while ended_at is null.

Deprecated fields and replacements
- None currently. The top-level fields run_id, path, created_at, artifacts are retained for backward compatibility.
- New consumers SHOULD rely on execution fields for lifecycle information.

Examples

Succeeded
{
  "run_id": "2025-01-01_000000Z-success000",
  "path": "/abs/path/to/runs/2025-01-01_000000Z-success000",
  "created_at": "2025-01-01T00:00:00Z",
  "artifacts": [
    {
      "name": "artifacts/report.md",
      "size": 1234,
      "modified_at": "2025-01-01T00:10:00Z",
      "url": "http://localhost:8001/runs/2025-01-01_000000Z-success000/artifacts/artifacts/report.md"
    }
  ],
  "schema_version": 2,
  "execution": {
    "status": "succeeded",
    "started_at": "2025-01-01T00:00:01Z",
    "ended_at": "2025-01-01T00:10:00Z",
    "duration_ms": 599000,
    "error_type": null,
    "error_message": null,
    "cancelled_at": null,
    "cancellation_reason": null,
    "version": 5
  }
}

Failed
{
  "run_id": "2025-01-01_000100Z-failed000",
  "path": "/abs/path/to/runs/2025-01-01_000100Z-failed000",
  "created_at": "2025-01-01T00:00:00Z",
  "artifacts": [],
  "schema_version": 2,
  "execution": {
    "status": "failed",
    "started_at": "2025-01-01T00:00:02Z",
    "ended_at": "2025-01-01T00:05:00Z",
    "duration_ms": 298000,
    "error_type": "PipelineError",
    "error_message": "PipelineError:Boom",
    "cancelled_at": null,
    "cancellation_reason": null,
    "version": 7
  }
}

Cancelled
{
  "run_id": "2025-01-01_000200Z-cancelled000",
  "path": "/abs/path/to/runs/2025-01-01_000200Z-cancelled000",
  "created_at": "2025-01-01T00:00:00Z",
  "artifacts": [],
  "schema_version": 2,
  "execution": {
    "status": "cancelled",
    "started_at": "2025-01-01T00:00:03Z",
    "ended_at": "2025-01-01T00:02:00Z",
    "duration_ms": 117000,
    "error_type": null,
    "error_message": null,
    "cancelled_at": "2025-01-01T00:02:00Z",
    "cancellation_reason": "user_request",
    "version": 11
  }
}

Unknown (missing status.json)
{
  "run_id": "2025-01-01_000300Z-legacy000",
  "path": "/abs/path/to/runs/2025-01-01_000300Z-legacy000",
  "created_at": "2025-01-01T00:00:00Z",
  "artifacts": [],
  "schema_version": 2,
  "execution": {
    "status": "unknown",
    "started_at": null,
    "ended_at": null,
    "duration_ms": null,
    "error_type": null,
    "error_message": null,
    "cancelled_at": null,
    "cancellation_reason": null,
    "version": null
  }
}

Notes for CI and determinism
- Tests should use an isolated temporary runs directory and ASGITransport with httpx to avoid real network I/O.
- Avoid sleeps; prefer deterministic timestamps in fixtures.
- No orchestrator execution is required for schema validation.

Migration guidance
- Clients previously relying on missing schema_version should now observe schema_version=2 in all RunDetail responses.
- Prefer execution fields over inferring lifecycle from other fields.
- Optionally start using execution.version to support CAS and cache invalidation logic on the UI. When version is null, fall back to standard HTTP cache mechanisms.

Change log
- v2: Added execution hydration, duration/error/cancellation fields, and version. Made schema_version explicit in all responses. Added robust defaulting for malformed or missing status.json.