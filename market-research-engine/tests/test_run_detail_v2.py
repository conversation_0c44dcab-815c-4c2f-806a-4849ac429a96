import json
from pathlib import Path
from typing import Any, Dict, Optional

import anyio
import pytest
from httpx import AsyncClient, ASGITransport

from market_research_engine.app import app  # FastAPI instance
from market_research_engine.config import settings
from market_research_engine.core.state import RunStatus, RunPhase
from market_research_engine.utils.paths import (
    artifacts_dir as _artifacts_dir,
    metadata_path as _metadata_path,
    status_path as _status_path,
)


@pytest.fixture(autouse=True)
def _isolate_runs_dir(tmp_path: Path, monkeypatch: pytest.MonkeyPatch):
    # Override runs directory to a temp location for hermetic tests
    monkeypatch.setattr(settings, "runs_dir", tmp_path, raising=False)
    monkeypatch.setattr(settings, "resolved_runs_dir", tmp_path, raising=False)
    tmp_path.mkdir(parents=True, exist_ok=True)
    yield


async def _write_run_fixture(
    run_root: Path,
    run_id: str,
    state: RunPhase,
    *,
    with_version: Optional[int] = 3,
    with_error: Optional[str] = None,
    cancelled: bool = False,
    cancellation_reason: Optional[str] = None,
    include_artifact: bool = True,
) -> Path:
    run_dir = run_root / run_id
    run_dir.mkdir(parents=True, exist_ok=True)

    # Minimal metadata
    meta = {
        "project": "testproj",
        "params": {"query": "abc", "region": "US"},
        "metadata": {},
        "created_at": "2025-01-01T00:00:00Z",
        "run_id": run_id,
        "run_dir": str(run_dir),
        "app_env": "test",
        "idempotency_key": None,
    }
    _metadata_path(run_dir).write_text(json.dumps(meta), encoding="utf-8")

    # Status build
    st = RunStatus(run_id=run_id, state=RunPhase.queued)
    st.transition(RunPhase.running, note="start")
    if with_error:
        st.error = with_error
    if state == RunPhase.succeeded:
        st.transition(RunPhase.succeeded, note="done")
    elif state == RunPhase.failed:
        st.transition(RunPhase.failed, note="error")
    elif state == RunPhase.cancelled:
        if cancelled:
            st.cancel(reason=cancellation_reason or "user_request", cancelled_by="user")
        else:
            # force terminal cancelled via transition to simulate partial systems
            st.transition(RunPhase.cancelled, note=cancellation_reason or "user_request")
            st.cancellation_reason = cancellation_reason or "user_request"

    if with_version is not None:
        # ensure version is set to requested value for CAS exposure
        st.version = int(with_version)

    _status_path(run_dir).write_text(json.dumps(st.to_dict()), encoding="utf-8")

    # Optional artifact
    if include_artifact:
        art_dir = _artifacts_dir(run_dir)
        art_dir.mkdir(parents=True, exist_ok=True)
        (art_dir / "status.json").write_text("{}", encoding="utf-8")  # sample non-internal artifact name reuse
        (art_dir / "report.md").write_text("# Report", encoding="utf-8")

    return run_dir


@pytest.mark.anyio
async def test_rundetail_v2_succeeded_hydration():
    runs_root = settings.resolved_runs_dir
    run_id = "2025-01-01_000000Z-success000"
    await _write_run_fixture(
        runs_root,
        run_id,
        RunPhase.succeeded,
        with_version=5,
        include_artifact=True,
    )

    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as client:
        r = await client.get(f"/runs/{run_id}")
        assert r.status_code == 200
        body = r.json()

    assert body["run_id"] == run_id
    assert body["schema_version"] == 2
    assert "artifacts" in body
    exec_meta = body.get("execution") or {}
    assert exec_meta.get("status") == "succeeded"
    assert isinstance(exec_meta.get("started_at"), str)
    assert isinstance(exec_meta.get("ended_at"), str)
    # duration can be None if timestamps malformed; here should be int or None
    assert (exec_meta.get("duration_ms") is None) or isinstance(exec_meta.get("duration_ms"), int)
    # new: version exposure
    assert exec_meta.get("version") == 5
    # error fields should be absent/None
    assert exec_meta.get("error_type") in (None, "PipelineError")  # tolerant
    assert exec_meta.get("error_message") in (None, str(exec_meta.get("error_message")))


@pytest.mark.anyio
async def test_rundetail_v2_failed_with_error_surface():
    runs_root = settings.resolved_runs_dir
    run_id = "2025-01-01_000100Z-failed000"
    await _write_run_fixture(
        runs_root,
        run_id,
        RunPhase.failed,
        with_version=7,
        with_error="PipelineError:Boom",
        include_artifact=False,
    )

    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as client:
        r = await client.get(f"/runs/{run_id}")
        assert r.status_code == 200
        body = r.json()

    assert body["schema_version"] == 2
    exec_meta = body.get("execution") or {}
    assert exec_meta.get("status") == "failed"
    assert exec_meta.get("error_type") == "PipelineError"
    assert exec_meta.get("error_message") == "PipelineError:Boom"
    assert exec_meta.get("version") == 7


@pytest.mark.anyio
async def test_rundetail_v2_cancelled_with_reason_and_defaults():
    runs_root = settings.resolved_runs_dir
    run_id = "2025-01-01_000200Z-cancelled000"
    await _write_run_fixture(
        runs_root,
        run_id,
        RunPhase.cancelled,
        with_version=11,
        cancelled=True,
        cancellation_reason="user_request",
        include_artifact=False,
    )

    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as client:
        r = await client.get(f"/runs/{run_id}")