# Market Research Engine (FastAPI + CrewAI)

A FastAPI backend scaffold with CrewAI integration placeholders for market research workflows.

## Features
- FastAPI app with health endpoint and `POST /runs`
- Input validation with Pydantic
- Run manager creates timestamped run folder under `./market-research-engine/runs/{timestamp}-{uuid}`
- Config via environment variables and `.env`
- Structured logging
- Placeholders for Agents/Tools/Services and CrewAI integration
- CLI to run the server

## Requirements
- Python 3.10+
- Install with: `pip install -e .[dev]` (use uv/pip/poetry as preferred)

## Environment
Copy and edit `.env.sample` to `.env`:

```
cp .env.sample .env
```

## Run server
Using uvicorn:

```
uvicorn market_research_engine.app:app --reload --port 8000
```

Or via CLI:

```
python -m market_research_engine.cli serve --port 8000 --reload
```

## API
- GET `/health`: returns `{"status":"ok"}`
- POST `/runs`: creates new run directory
  - Request body:
    ```json
    {
      "project": "string (required)",
      "params": {
        "query": "optional arbitrary params"
      },
      "metadata": {
        "requested_by": "optional"
      }
    }
    ```
  - Response:
    ```json
    {
      "run_id": "2025-08-04_200410Z-1b2c3d4e5f",
      "path": "runs/2025-08-04_200410Z-1b2c3d4e5f",
      "created_at": "2025-08-04T20:04:10.896Z"
    }
    ```

## Project layout
- `market_research_engine/app.py` FastAPI application
- `market_research_engine/services/runs_service.py` Run directory management
- `market_research_engine/agents/*` CrewAI scaffolding
- `market_research_engine/tools/*` Tooling scaffolding
- `market_research_engine/config.py` Settings loader
- `market_research_engine/logging.py` Logging setup
- `market_research_engine/cli.py` Simple CLI
