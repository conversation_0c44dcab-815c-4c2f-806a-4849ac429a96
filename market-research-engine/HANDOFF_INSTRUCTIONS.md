# Handoff Instructions

## Setup Steps

1. **Backend Fixes**:
   - Ensure Tailwind entry CSS imports token utilities (e.g., `src/styles/tailwind.css` includes `@import "./tokens.css";` then `@tailwind base;` `@tailwind components;` `@tailwind utilities;`).
   - If tokens file doesn't exist, add `src/styles/tokens.css` with custom utility classes or CSS variables as needed by components.

2. **Restart and Validate**:
   - Backend: run FastAPI (Typer CLI loads uvicorn)
   - Frontend: Vite dev on 5173
   - In browser:
     - Enter query, region, depth, click Run.
     - Confirm POST /runs 200/202.
     - Confirm GET /runs/latest/artifacts/synthesis.json 200 within a few seconds.
     - UI panels populate without "Failed to fetch".

## Known Good References

- Backend routing and request schema:
  - `market_research_engine/routing.py:265-356`
- Synthesis artifact structure for Opportunities tab:
  - `market_research_engine/services/dashboard_formatter.py:253-308`
- Synthesis agent hardening:
  - `market_research_engine/pipeline/agents/synthesis.py:74-127`

## Acceptance Criteria

- Frontend no longer logs any requests to http://localhost:8001.
- Run POST request uses the correct JSON shape; backend accepts and processes successfully.
- Latest synthesis loads and displays within the UI (summary, recommendations, blueprint).
- UI is styled (not raw/unformatted) consistent with Tailwind and tokens.

## If Productionizing Next

- Move runs persistence from filesystem to S3 + Postgres (as planned).
- Add auth (JWT/OAuth).
- Add observability (structured logs, metrics dashboards).
- Lock the API contract with OpenAPI spec and generate TS types for the frontend client.

## Troubleshooting

If "Failed to fetch" persists: 
- Open devtools Network tab; check the exact URL and status. 
- If 404, it's a timing issue (run not ready). 
- If net::ERR_CONNECTION_REFUSED, there's still a host/port mismatch. 
- If 422, your POST body is wrong (likely missing params.query).

This handoff should let you stabilize the local E2E path quickly and avoid further backend/frontend mismatch on URLs and request payloads.
