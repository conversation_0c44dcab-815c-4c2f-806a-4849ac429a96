[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "market-research-engine"
version = "0.1.0"
description = "FastAPI backend with CrewAI integration for market research runs"
readme = "README.md"
requires-python = ">=3.10"
authors = [{ name = "Ardi", email = "<EMAIL>" }]
dependencies = [
    "fastapi>=0.115.0",
    "uvicorn[standard]>=0.30.0",
    "pydantic>=2.7.0",
    "python-dotenv>=1.0.1",
    "httpx>=0.27.0",
    "loguru>=0.7.2",
    "structlog>=24.1.0",
    "typer>=0.12.3",
    "crewai>=0.51.0",
    "openai>=1.40.0",
    "pytrends>=4.9.2",
    "google-api-python-client>=2.130.0",
    "google-auth>=2.33.0",
    "youtube-transcript-api>=0.6.2",
    "opencv-python-headless>=*********",
    "numpy>=1.26.4",
    "pillow>=10.4.0",
    "tenacity>=8.5.0"
]

[tool.setuptools.packages.find]
# Limit discovery to the single intended package and exclude run outputs
where = ["."]
include = ["market_research_engine*"]
exclude = ["runs*", "tests*", "scripts*"]

[tool.setuptools.package-data]
# Include non-Python files under the package if needed (e.g., templates)
"market_research_engine" = ["py.typed"]

[project.optional-dependencies]
dev = [
    "ruff>=0.4.8",
    "mypy>=1.10.0",
    "pytest>=8.2.0",
    "pytest-asyncio>=0.23.7",
    "anyio>=4.4.0",
    "types-python-dateutil",
]

[tool.ruff]
line-length = 100
target-version = "py310"
select = ["E", "F", "I", "UP", "B"]
ignore = []

[tool.mypy]
python_version = "3.10"
warn_unused_configs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
strict_optional = true
plugins = []

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = "-q"