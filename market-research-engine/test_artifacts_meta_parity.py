#!/usr/bin/env python3
"""
Test script to validate artifacts_meta parity between POST /runs and GET /runs endpoints.
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8001"

def test_artifacts_meta_parity():
    """Test that POST and GET return identical artifacts_meta."""
    print("🧪 Testing artifacts_meta parity between POST and GET endpoints...")
    
    # 1. Create a new run via POST
    print("\n1. Creating new run via POST /runs...")
    post_data = {
        "project": "parity-test",
        "params": {"query": "test parity", "region": "US", "depth": "lite"}
    }
    headers = {
        "Content-Type": "application/json",
        "X-Idempotency-Key": "parity-test-key"
    }
    
    try:
        post_response = requests.post(f"{BASE_URL}/runs", json=post_data, headers=headers)
        post_response.raise_for_status()
        post_data_result = post_response.json()
        run_id = post_data_result.get("run_id")
        post_artifacts_meta = post_data_result.get("artifacts_meta")
        
        print(f"✅ POST created run: {run_id}")
        print(f"📦 POST artifacts_meta: {json.dumps(post_artifacts_meta, indent=2)}")
        
    except Exception as e:
        print(f"❌ POST request failed: {e}")
        return False
    
    # 2. Fetch the same run via GET /runs/{id}
    print(f"\n2. Fetching run via GET /runs/{run_id}...")
    try:
        get_response = requests.get(f"{BASE_URL}/runs/{run_id}")
        get_response.raise_for_status()
        get_data_result = get_response.json()
        get_artifacts_meta = get_data_result.get("artifacts_meta")
        
        print(f"✅ GET fetched run: {run_id}")
        print(f"📦 GET artifacts_meta: {json.dumps(get_artifacts_meta, indent=2)}")
        
    except Exception as e:
        print(f"❌ GET request failed: {e}")
        return False
    
    # 3. Compare artifacts_meta
    print(f"\n3. Comparing artifacts_meta...")
    if post_artifacts_meta == get_artifacts_meta:
        print("✅ PARITY TEST PASSED: artifacts_meta identical between POST and GET")
        return True
    else:
        print("❌ PARITY TEST FAILED: artifacts_meta differs between POST and GET")
        print(f"POST: {post_artifacts_meta}")
        print(f"GET:  {get_artifacts_meta}")
        return False
    
    # 4. Test GET /runs/latest
    print(f"\n4. Testing GET /runs/latest...")
    try:
        latest_response = requests.get(f"{BASE_URL}/runs/latest")
        latest_response.raise_for_status()
        latest_data = latest_response.json()
        latest_artifacts_meta = latest_data.get("artifacts_meta")
        
        print(f"✅ GET /runs/latest fetched run: {latest_data.get('run_id')}")
        print(f"📦 Latest artifacts_meta: {json.dumps(latest_artifacts_meta, indent=2)}")
        
        # Should match our new run since it's the latest
        if latest_artifacts_meta == post_artifacts_meta:
            print("✅ Latest run artifacts_meta matches POST")
        else:
            print("⚠️  Latest run artifacts_meta differs (may be different run)")
            
    except Exception as e:
        print(f"❌ GET /runs/latest failed: {e}")

if __name__ == "__main__":
    success = test_artifacts_meta_parity()
    sys.exit(0 if success else 1)
