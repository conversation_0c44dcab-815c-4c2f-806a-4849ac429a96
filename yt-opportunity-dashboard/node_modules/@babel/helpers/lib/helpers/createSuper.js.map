{"version": 3, "names": ["_getPrototypeOf", "require", "_isNativeReflectConstruct", "_possibleConstructorReturn", "_createSuper", "Derived", "hasNativeReflectConstruct", "isNativeReflectConstruct", "_createSuperInternal", "Super", "getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "Reflect", "construct", "arguments", "apply", "possibleConstructorReturn"], "sources": ["../../src/helpers/createSuper.js"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nimport getPrototypeOf from \"getPrototypeOf\";\nimport isNativeReflectConstruct from \"isNativeReflectConstruct\";\nimport possibleConstructorReturn from \"possibleConstructorReturn\";\n\nexport default function _createSuper(Derived) {\n  var hasNativeReflectConstruct = isNativeReflectConstruct();\n\n  return function _createSuperInternal() {\n    var Super = getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      // NOTE: This doesn't work if this.__proto__.constructor has been modified.\n      var NewTarget = getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return possibleConstructorReturn(this, result);\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAEe,SAASG,YAAYA,CAACC,OAAO,EAAE;EAC5C,IAAIC,yBAAyB,GAAGC,yBAAwB,CAAC,CAAC;EAE1D,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IACrC,IAAIC,KAAK,GAAGC,eAAc,CAACL,OAAO,CAAC;MACjCM,MAAM;IACR,IAAIL,yBAAyB,EAAE;MAE7B,IAAIM,SAAS,GAAGF,eAAc,CAAC,IAAI,CAAC,CAACG,WAAW;MAChDF,MAAM,GAAGG,OAAO,CAACC,SAAS,CAACN,KAAK,EAAEO,SAAS,EAAEJ,SAAS,CAAC;IACzD,CAAC,MAAM;MACLD,MAAM,GAAGF,KAAK,CAACQ,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IACvC;IACA,OAAOE,0BAAyB,CAAC,IAAI,EAAEP,MAAM,CAAC;EAChD,CAAC;AACH", "ignoreList": []}