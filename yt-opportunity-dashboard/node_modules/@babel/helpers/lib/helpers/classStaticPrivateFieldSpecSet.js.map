{"version": 3, "names": ["_classApplyDescriptorSet", "require", "_assert<PERSON>lassBrand", "_classCheckPrivateStaticFieldDescriptor", "_classStaticPrivateFieldSpecSet", "receiver", "classConstructor", "descriptor", "value", "assertClassBrand", "classCheckPrivateStaticFieldDescriptor", "classApplyDescriptorSet"], "sources": ["../../src/helpers/classStaticPrivateFieldSpecSet.js"], "sourcesContent": ["/* @minVersion 7.0.2 */\n/* @onlyBabel7 */\n\nimport classApplyDescriptorSet from \"classApplyDescriptorSet\";\nimport assertClassBrand from \"assertClassBrand\";\nimport classCheckPrivateStaticFieldDescriptor from \"classCheckPrivateStaticFieldDescriptor\";\nexport default function _classStaticPrivateFieldSpecSet(\n  receiver,\n  classConstructor,\n  descriptor,\n  value,\n) {\n  assertClassBrand(classConstructor, receiver);\n  classCheckPrivateStaticFieldDescriptor(descriptor, \"set\");\n  classApplyDescriptorSet(receiver, descriptor, value);\n  return value;\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,wBAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,uCAAA,GAAAF,OAAA;AACe,SAASG,+BAA+BA,CACrDC,QAAQ,EACRC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,EACL;EACAC,iBAAgB,CAACH,gBAAgB,EAAED,QAAQ,CAAC;EAC5CK,uCAAsC,CAACH,UAAU,EAAE,KAAK,CAAC;EACzDI,wBAAuB,CAACN,QAAQ,EAAEE,UAAU,EAAEC,KAAK,CAAC;EACpD,OAAOA,KAAK;AACd", "ignoreList": []}