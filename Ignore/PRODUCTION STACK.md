## PRODUCTION STACK

### VIDEO CREATION
- **Visuals:** AI-generated imagery (Midjourney V7 primary, DALL-E/Stable Diffusion backup)
- **Video Editing:** [User to specify - likely <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, or CapCut]
- **Motion Graphics:** Simple animations + AI-generated atmospheric effects
- **Format:** 16:9 horizontal for YouTube
- **Length:** 10-15 minutes target

### AUDIO
- **Narration:** AI voice or user recording
- **Music:** Copyright-free libraries
- **Sound Effects:** Minimal, only when essential

### RESEARCH & WRITING
- **Research:** Web sources, academic papers, verified databases
- **Script Writing:** Claude (in 2-minute chunks)
- **Fact-Checking:** Manual verification required

## VISUAL CAPABILITIES (UPDATED FOR MIDJOURNEY V7)

### WHAT WE CAN CREATE
✅ **Static Images:**
- Historical photos/paintings
- Maps with overlays
- Simple diagrams
- Charts and graphs
- Text overlays
- Before/after comparisons
- Icons and symbols

✅ **Atmospheric Scenes (NEW):**
- Environmental disasters in progress
- Particle effects (ash, dust, smoke, fog)
- Weather phenomena
- Economic collapse visuals
- Historical recreations with mood
- Corporate/industrial atmospheres

✅ **Simple Animations:**
- <PERSON> Burns effect (pan/zoom on images)
- Basic transitions
- Text animations (appear/disappear)
- Arrow movements on maps
- Graph animations (bars growing)
- 4-10 second clips from AI variations

✅ **Documentary-Style Visuals:**
- Photojournalistic scenes
- Environmental storytelling
- Before/after states
- Atmospheric depth
- Mood-driven imagery

### WHAT WE CANNOT CREATE
❌ **Complex Animations:**
- Character animation
- Complex 3D models
- Liquid simulations
- Technical schematics

❌ **Specific Elements:**
- Exact historical figures' faces
- Real people's likenesses
- Brand logos (copyright)
- Complex human interactions
- Readable text in scenes

❌ **Technical Limitations:**
- No green screen work
- No multi-camera setups
- No on-location filming
- No interviews or talking heads

## AUDIO LIMITATIONS

### NARRATION
- Single voice throughout
- No dialogue or character voices
- Consistent tone and pacing
- No dramatic performance

### SOUND DESIGN
- Background music only
- Simple sound effects (swooshes, clicks)
- No complex soundscapes
- No copyrighted music

## SCRIPT REQUIREMENTS FOR PRODUCTION

### VISUAL DESCRIPTIONS (UPDATED)
**Good:** "Volcanic ash falling on ancient farmland"
**Good:** "Empty shelves in modern grocery store"
**Good:** "Dust storm approaching 1930s farm"
**Bad:** "Cleopatra speaking to advisors"

**Good:** "Corporate data center at night, ominous mood"
**Good:** "Ancient marketplace during sandstorm"
**Good:** "Modern city during economic collapse"
**Bad:** "People's facial expressions changing"

### MIDJOURNEY-SPECIFIC DESCRIPTIONS
- Include atmospheric elements
- Specify documentary/photojournalistic style
- Add mood descriptors
- Note particle effects when relevant

### PACING FOR EDITING
- Change visual every 3-5 seconds
- Major transition every 30-45 seconds
- New section every 2-3 minutes
- Hook must have 5-6 visual changes in first 30 seconds
- Atmospheric scenes can hold for 4-10 seconds

### NARRATION GUIDELINES
- Natural pauses every 2-3 sentences
- Emphasis points clearly marked
- No complex pronunciations without phonetics
- Breathing room between major points
- Allow atmospheric visuals to breathe

## PRODUCTION WORKFLOW CONSTRAINTS

### TIME REQUIREMENTS (UPDATED)
- Research: 2-4 hours per video
- Script Writing: 2-3 hours (with revisions)
- Image Generation: 4-6 hours (including variations)
- Video Editing: 4-6 hours
- Total: ~16-18 hours per video

### QUALITY CONTROLS
- Every fact must have a source
- Every visual must be copyright-free
- Every claim must be defensible
- No speculation without clear labeling
- Test complex visuals before scripting

### BOTTLENECKS
- AI image generation (multiple attempts/variations)
- Fact verification (time-consuming)
- Creating movement from stills
- Maintaining visual variety
- Rendering time

## COMPETITIVE ADVANTAGES (UPDATED)

### WHAT WE DO WELL
- Atmospheric storytelling
- Unique angles competitors miss
- Documentary-style visuals
- Environmental/economic scenes
- Historical mood pieces
- Quick turnaround

### WHAT WE CAN'T COMPETE ON
- Character-driven narratives
- On-location footage
- Celebrity interviews
- Technical 3D animations
- Brand-specific content

## OPTIMIZATION OPPORTUNITIES

### TEMPLATES TO DEVELOP
- Midjourney prompt library
- Standard map animations
- Reusable graph styles
- Transition library
- Music beds for different moods
- Particle effect variations

### EFFICIENCY GAINS
- Batch AI image generation
- Script templates for common structures
- Pre-made intro/outro
- Keyword research system
- Saved successful prompts

## THE REALITY CHECK (UPDATED)

We're making documentary-quality content with:
- No team
- No budget
- Powerful AI visual tools
- Smart angles
- Atmospheric storytelling

This means:
- Leverage Midjourney V7's strengths
- Focus on mood and atmosphere
- Use particles/weather for movement
- Play to documentary aesthetics
- Tell stories through environments

## NEW VISUAL STRATEGY

Instead of avoiding complex visuals, we now:
- Create atmospheric scenes that support narration
- Use environmental storytelling
- Show abstract concepts through mood
- Generate multiple variations for movement
- Think "documentary filmmaker with AI"

## REMEMBER

We can't show specific people doing specific things, but we CAN show the world they lived in, the atmosphere they breathed, the collapse they witnessed. That's often more powerful than character close-ups.

Constraints still breed creativity - but we have fewer constraints than we thought.