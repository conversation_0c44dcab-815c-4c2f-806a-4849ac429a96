{"permissions": {"allow": ["<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(.venv/bin/python:*)", "Bash(PYTHONPATH=.venv/lib/python3.13/site-packages .venv/bin/python -c \"\nimport sys\nsys.path.insert(0, '.')\nfrom market_research_engine.config import settings\nprint('Live providers:', settings.live_providers)  \nprint('YouTube key present:', bool(settings.youtube_api_key))\nprint('OpenRouter key present:', bool(settings.openrouter_api_key))\n\")", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(pkill:*)", "Bash(grep:*)", "Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(LIVE_PROVIDERS=true OPENROUTER_API_KEY=\"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\" YOUTUBE_API_KEY=\"AIzaSyB2nVdgNOpEXTjGMJLXKoqBJeQyu5Yw4Ao\" OPENROUTER_MODEL=\"gpt-4o-mini\" python -c \"\nimport sys\nsys.path.insert(0, ''.'')\nfrom market_research_engine.services.transcripts_service import TranscriptsService\n\nprint(''=== TRANSCRIPT SERVICE DEBUG ==='')\n\n# Test the transcript service directly\ntranscript_service = TranscriptsService()\nprint(f''TranscriptsService initialized: {transcript_service is not None}'')\n\n# Test with a known video that should have transcripts\ntest_video_id = ''dQw4w9WgXcQ''  # Rick Roll - definitely has transcripts\nprint(f''Testing with video ID: {test_video_id}'')\n\ntry:\n    transcript = transcript_service.get_transcript(test_video_id)\n    if transcript:\n        print(f''✅ SUCCESS: Got transcript ({len(transcript)} chars)'')\n        print(f''Preview: {transcript[:200]}...'')\n    else:\n        print(''❌ FAILED: No transcript returned'')\nexcept Exception as e:\n    print(f''❌ ERROR: {e}'')\n    import traceback\n    traceback.print_exc()\n\n# Test youtube_transcript_api directly\nprint(''\\n=== DIRECT LIBRARY TEST ==='')\ntry:\n    from youtube_transcript_api import YouTubeTranscriptApi\n    print(''✅ youtube_transcript_api imported successfully'')\n    \n    direct_transcript = YouTubeTranscriptApi.get_transcript(test_video_id)\n    if direct_transcript:\n        print(f''✅ Direct API SUCCESS: {len(direct_transcript)} segments'')\n        full_text = '' ''.join([seg.get(''text'', '''') for seg in direct_transcript])\n        print(f''Full text length: {len(full_text)} chars'')\n        print(f''Preview: {full_text[:200]}...'')\n    else:\n        print(''❌ Direct API returned empty'')\n        \nexcept Exception as e:\n    print(f''❌ Direct API ERROR: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(LIVE_PROVIDERS=true OPENROUTER_API_KEY=\"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\" YOUTUBE_API_KEY=\"AIzaSyB2nVdgNOpEXTjGMJLXKoqBJeQyu5Yw4Ao\" OPENROUTER_MODEL=\"gpt-4o-mini\" python -c \"\nimport sys\nsys.path.insert(0, ''.'')\nimport json\nfrom pathlib import Path\nfrom market_research_engine.pipeline.agents.vision import make_vision_agent\n\n# Test with your actual data\nrecon_path = Path(''/Users/<USER>/Desktop/direrctor/test-results/recon.json'')\nwith open(recon_path) as f:\n    recon_data = json.load(f)\n\nprint(''=== TESTING TRANSCRIPT FIX ==='')\nprint(f''Videos in recon: {len(recon_data[\"\"items\"\"])}'')\n\n# Check transcript availability in recon data\ntranscript_count = 0\nfor item in recon_data[''items'']:\n    if item.get(''transcript_snippet''):\n        transcript_count += 1\n        print(f''✅ Video {item[\"\"video_id\"\"]}: has transcript_snippet ({len(item[\"\"transcript_snippet\"\"])} chars)'')\n    else:\n        print(f''❌ Video {item[\"\"video_id\"\"]}: no transcript'')\n\nprint(f''\\nTotal with transcript_snippet: {transcript_count}/{len(recon_data[\"\"items\"\"])}'')\n\n# Test VisionAgent with this data\nimport tempfile\ntemp_dir = Path(tempfile.mkdtemp())\ntest_dir = temp_dir / ''test''\nartifacts_dir = test_dir / ''artifacts''\nartifacts_dir.mkdir(parents=True)\n\n# Copy recon data\nwith open(artifacts_dir / ''recon.json'', ''w'') as f:\n    json.dump(recon_data, f)\n\n# Test vision agent\nimport asyncio\nasync def test_vision():\n    vision_agent = make_vision_agent(runs_dir=temp_dir)\n    await vision_agent.run(run_dir=test_dir, query=''AI video editing tools'', region=''US'')\n    \n    # Check results\n    with open(artifacts_dir / ''vision.json'') as f:\n        vision_data = json.load(f)\n    \n    transcript_analysis = vision_data.get(''transcript_analysis'', {})\n    coverage = transcript_analysis.get(''transcript_availability'', {})\n    \n    print(f''\\n=== VISION AGENT TEST RESULTS ==='')\n    print(f''Videos with transcripts: {coverage.get(\"\"videos_with_transcripts\"\", 0)}/{coverage.get(\"\"total_videos\"\", 0)}'')\n    print(f''Content themes: {len(transcript_analysis.get(\"\"content_themes\"\", []))}'')\n    print(f''Themes: {transcript_analysis.get(\"\"content_themes\"\", [])}'')\n    \n    return coverage.get(''videos_with_transcripts'', 0) > 0\n\nsuccess = asyncio.run(test_vision())\nprint(f''\\n✅ TRANSCRIPT FIX: {\"\"SUCCESS\"\" if success else \"\"FAILED\"\"}'')\n\")", "Bash(LIVE_PROVIDERS=true OPENROUTER_API_KEY=\"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\" YOUTUBE_API_KEY=\"AIzaSyB2nVdgNOpEXTjGMJLXKoqBJeQyu5Yw4Ao\" OPENROUTER_MODEL=\"gpt-4o-mini\" python -c \"\nimport sys\nsys.path.insert(0, ''.'')\nimport json\nimport tempfile\nimport asyncio\nfrom pathlib import Path\nfrom market_research_engine.pipeline.agents.vision import make_vision_agent\n\n# Test the fix\ntemp_dir = Path(tempfile.mkdtemp())\ntest_dir = temp_dir / ''test''\nartifacts_dir = test_dir / ''artifacts''\nartifacts_dir.mkdir(parents=True)\n\n# Copy your real recon data\nwith open(''/Users/<USER>/Desktop/direrctor/test-results/recon.json'') as f:\n    recon_data = json.load(f)\n\nwith open(artifacts_dir / ''recon.json'', ''w'') as f:\n    json.dump(recon_data, f)\n\nprint(''=== TESTING FIXED TRANSCRIPT PROCESSING ==='')\n\nasync def test_fixed_vision():\n    vision_agent = make_vision_agent(runs_dir=temp_dir)\n    await vision_agent.run(run_dir=test_dir, query=''AI video editing tools'', region=''US'')\n    \n    # Check results\n    with open(artifacts_dir / ''vision.json'') as f:\n        vision_data = json.load(f)\n    \n    transcript_analysis = vision_data.get(''transcript_analysis'', {})\n    coverage = transcript_analysis.get(''transcript_availability'', {})\n    themes = transcript_analysis.get(''content_themes'', [])\n    \n    print(f''Videos with transcripts: {coverage.get(\"\"videos_with_transcripts\"\", 0)}/{coverage.get(\"\"total_videos\"\", 0)}'')\n    print(f''Content themes found: {len(themes)}'')\n    if themes:\n        print(f''Sample themes: {themes[:3]}'')\n    \n    hooks = transcript_analysis.get(''successful_hooks'', [])\n    keywords = transcript_analysis.get(''actionable_keywords'', [])\n    \n    print(f''Successful hooks: {len(hooks)}'')\n    print(f''Keywords: {len(keywords)}'')\n    \n    return coverage.get(''videos_with_transcripts'', 0) > 0\n\nsuccess = asyncio.run(test_fixed_vision())\nprint(f''\\n🎯 TRANSCRIPT FIX STATUS: {\"\"✅ SUCCESS\"\" if success else \"\"❌ FAILED\"\"}'')\n\")", "Bash(LIVE_PROVIDERS=true OPENROUTER_API_KEY=\"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\" YOUTUBE_API_KEY=\"AIzaSyB2nVdgNOpEXTjGMJLXKoqBJeQyu5Yw4Ao\" OPENROUTER_MODEL=\"gpt-4o-mini\" python -c \"\nimport sys\nsys.path.insert(0, ''.'')\nimport json\nimport tempfile\nfrom pathlib import Path\nfrom market_research_engine.pipeline.orchestrator import Orchestrator\n\n# Run complete workflow with transcript fix\ntemp_dir = Path(tempfile.mkdtemp())\nrun_id = ''fixed-transcript-test''\nrun_dir = temp_dir / run_id\nrun_dir.mkdir(parents=True)\n\n# Create status\nstatus_data = {\n    ''run_id'': run_id,\n    ''state'': ''running'',\n    ''version'': 1,\n    ''started_at'': ''2024-01-01T00:00:00Z''\n}\n\nwith open(run_dir / ''status.json'', ''w'') as f:\n    json.dump(status_data, f)\n\nprint(''=== COMPLETE WORKFLOW WITH TRANSCRIPT FIX ==='')\n\ntry:\n    orchestrator = Orchestrator()\n    orchestrator.run_sync(run_dir=run_dir, query=''video editing automation'', region=''US'')\n    \n    # Check final synthesis\n    synthesis_md = run_dir / ''artifacts'' / ''synthesis.md''\n    with open(synthesis_md) as f:\n        content = f.read()\n    \n    # Check for transcript insights\n    transcript_indicators = [''hook'', ''theme'', ''content creation'', ''automation'']\n    found_indicators = [ind for ind in transcript_indicators if ind.lower() in content.lower()]\n    \n    print(f''✅ Workflow completed successfully!'')\n    print(f''📊 Transcript insights in synthesis: {found_indicators}'')\n    print(f''📄 Synthesis length: {len(content)} characters'')\n    \n    # Show key sections\n    lines = content.split(''\\n'')\n    recommendations = []\n    in_recommendations = False\n    \n    for line in lines:\n        if ''## Recommendations'' in line:\n            in_recommendations = True\n            continue\n        elif line.startswith(''##''):\n            in_recommendations = False\n        elif in_recommendations and line.startswith(''- ''):\n            recommendations.append(line)\n    \n    print(f''\\n🎯 Enhanced Recommendations ({len(recommendations)} total):'')\n    for rec in recommendations[:5]:  # Show first 5\n        print(f''  {rec}'')\n    if len(recommendations) > 5:\n        print(f''  ... and {len(recommendations) - 5} more'')\n        \n    # Save results to accessible location\n    import shutil\n    result_dir = Path(''/Users/<USER>/Desktop/direrctor/enhanced-test-results'')\n    if result_dir.exists():\n        shutil.rmtree(result_dir)\n    shutil.copytree(run_dir / ''artifacts'', result_dir)\n    \n    print(f''\\n📁 Enhanced results saved to:'')\n    print(f''   /Users/<USER>/Desktop/direrctor/enhanced-test-results/'')\n    \nexcept Exception as e:\n    print(f''❌ Workflow failed: {e}'')\n\")", "Bash(LIVE_PROVIDERS=false python -c \"\nimport tempfile\nimport json\nfrom pathlib import Path\nfrom market_research_engine.pipeline.orchestrator import Orchestrator\nfrom market_research_engine.core.state import RunStatus, RunPhase\n\nprint(''=== TESTING COMPLETE ENHANCED WORKFLOW ==='')\n\n# Create test environment\ntemp_dir = Path(tempfile.mkdtemp())\nrun_id = ''complete-enhanced-test''\nrun_dir = temp_dir / run_id\nrun_dir.mkdir(parents=True)\n\n# Create status for orchestrator\nstatus_data = {\n    ''run_id'': run_id,\n    ''state'': ''running'',\n    ''version'': 1,\n    ''started_at'': ''2024-01-01T00:00:00Z''\n}\n\nwith open(run_dir / ''status.json'', ''w'') as f:\n    json.dump(status_data, f)\n\nprint(f''📁 Test directory: {run_dir}'')\n\n# Run complete enhanced workflow\ntry:\n    orchestrator = Orchestrator()\n    orchestrator.run_sync(run_dir=run_dir, query=''AI content creation tools'', region=''US'')\n    \n    artifacts_dir = run_dir / ''artifacts''\n    \n    # Check all artifacts were created\n    expected_files = [''recon.json'', ''vision.json'', ''synthesis.json'', ''synthesis.md'', ''dashboard.json'']\n    created_files = []\n    \n    for filename in expected_files:\n        filepath = artifacts_dir / filename\n        if filepath.exists():\n            created_files.append(filename)\n            size = filepath.stat().st_size\n            print(f''✅ {filename} ({size:,} bytes)'')\n        else:\n            print(f''❌ {filename} (missing)'')\n    \n    print(f''\\n📊 Created {len(created_files)}/{len(expected_files)} expected files'')\n    \n    # Test dashboard structure\n    if ''dashboard.json'' in created_files:\n        with open(artifacts_dir / ''dashboard.json'') as f:\n            dashboard_data = json.load(f)\n        \n        print(''\\n🎛️ DASHBOARD STRUCTURE:'')\n        print(f''  Query: {dashboard_data.get(\"\"query\"\", \"\"Unknown\"\")}'')\n        print(f''  Region: {dashboard_data.get(\"\"region\"\", \"\"Unknown\"\")}'')\n        \n        # Check three main tabs\n        comp_analysis = dashboard_data.get(''competition_analysis'', {})\n        trending = dashboard_data.get(''trending_content'', {})\n        opportunities = dashboard_data.get(''opportunities'', {})\n        \n        print(''\\n📈 Competition Analysis Tab:'')\n        competitors = comp_analysis.get(''competitors'', [])\n        print(f''  - Competitors found: {len(competitors)}'')\n        print(f''  - Niche averages available: {bool(comp_analysis.get(\"\"niche_averages\"\"))}'')\n        \n        print(''\\n🔥 Trending Content Tab:'')\n        trending_videos = trending.get(''trending_videos'', [])  \n        print(f''  - Trending videos: {len(trending_videos)}'')\n        print(f''  - High velocity videos: {trending.get(\"\"trending_summary\"\", {}).get(\"\"high_velocity_count\"\", 0)}'')\n        \n        print(''\\n🎯 Opportunities Tab:'')\n        opportunity_summary = opportunities.get(''opportunity_summary'', {})\n        print(f''  - Overall opportunity score: {opportunity_summary.get(\"\"overall_score\"\", 0)}%'')\n        print(f''  - Opportunity level: {opportunity_summary.get(\"\"opportunity_level\"\", \"\"unknown\"\")}'')\n        print(f''  - Strategic recommendations: {len(opportunities.get(\"\"strategic_recommendations\"\", []))}'')\n        \n        # Check video selection for analyzer handoff\n        video_selection = dashboard_data.get(''video_selection'', {})\n        selection_summary = video_selection.get(''selection_summary'', {})\n        print(''\\n🎬 Video Selection for Analysis:'')\n        print(f''  - Videos ready for analysis: {selection_summary.get(\"\"total_videos\"\", 0)}'')\n        print(f''  - Recommended for deep analysis: {selection_summary.get(\"\"recommended_for_analysis\"\", 0)}'')\n        print(f''  - Handoff ready: {video_selection.get(\"\"handoff_ready\"\", False)}'')\n        print(f''  - Top candidate: {selection_summary.get(\"\"top_candidate\"\", \"\"None\"\")}'')\n        \n        print(''\\n✅ Dashboard integration: SUCCESS'')\n    else:\n        print(''\\n❌ Dashboard integration: FAILED'')\n    \nexcept Exception as e:\n    print(f''❌ Workflow failed: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(LIVE_PROVIDERS=true OPENROUTER_API_KEY=\"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\" YOUTUBE_API_KEY=\"AIzaSyB2nVdgNOpEXTjGMJLXKoqBJeQyu5Yw4Ao\" OPENROUTER_MODEL=\"gpt-4o-mini\" python -c \"\nimport tempfile\nimport json\nfrom pathlib import Path\nfrom market_research_engine.pipeline.orchestrator import Orchestrator\n\nprint(''=== TESTING LIVE ENHANCED WORKFLOW ==='')\n\ntemp_dir = Path(tempfile.mkdtemp())\nrun_id = ''live-enhanced-test''\nrun_dir = temp_dir / run_id\nrun_dir.mkdir(parents=True)\n\nstatus_data = {\n    ''run_id'': run_id,\n    ''state'': ''running'',\n    ''version'': 1,\n    ''started_at'': ''2024-01-01T00:00:00Z''\n}\n\nwith open(run_dir / ''status.json'', ''w'') as f:\n    json.dump(status_data, f)\n\nprint(f''📁 Test directory: {run_dir}'')\n\ntry:\n    orchestrator = Orchestrator()\n    orchestrator.run_sync(run_dir=run_dir, query=''YouTube automation'', region=''US'')\n    \n    artifacts_dir = run_dir / ''artifacts''\n    \n    # Check dashboard results\n    with open(artifacts_dir / ''dashboard.json'') as f:\n        dashboard_data = json.load(f)\n    \n    print(''\\n🎛️ LIVE ENHANCED RESULTS:'')\n    print(f''Query: {dashboard_data.get(\"\"query\"\", \"\"Unknown\"\")}'')\n    \n    # Market overview\n    market_overview = dashboard_data.get(''market_overview'', {})\n    market_health = market_overview.get(''market_health'', {})\n    \n    print(f''\\n📊 Market Health:'')\n    print(f''  - Market Staleness: {market_health.get(\"\"staleness_index\"\", 0):.1f}/100'')\n    print(f''  - Market Freshness: {market_health.get(\"\"freshness_score\"\", 0):.1f}/100'')\n    print(f''  - Trend Velocity: {market_health.get(\"\"trend_velocity\"\", \"\"N/A\"\")}'')\n    print(f''  - Faceless Viability: {market_health.get(\"\"faceless_viability\"\", 0):.1f}/100'')\n    \n    # Competition analysis\n    comp_analysis = dashboard_data.get(''competition_analysis'', {})\n    competitors = comp_analysis.get(''competitors'', [])\n    print(f''\\n🏆 Top 3 Competitors:'')\n    for i, comp in enumerate(competitors[:3]):\n        tier = comp.get(''performance_tier'', ''standard'')\n        emoji = ''🔥'' if tier == ''breakout'' else ''📈'' if tier == ''above_average'' else ''📊''\n        print(f''  {i+1}. {emoji} {comp.get(\"\"channel_title\"\", \"\"Unknown\"\")} ({comp.get(\"\"avg_views\"\", 0):,} avg views)'')\n    \n    # Opportunities\n    opportunities = dashboard_data.get(''opportunities'', {})\n    opp_summary = opportunities.get(''opportunity_summary'', {})\n    print(f''\\n🎯 Market Opportunities:'')\n    print(f''  - Overall Score: {opp_summary.get(\"\"overall_score\"\", 0):.1f}/100'')\n    print(f''  - Opportunity Level: {opp_summary.get(\"\"opportunity_level\"\", \"\"unknown\"\").upper()}'')\n    print(f''  - Strategic Recommendations: {len(opportunities.get(\"\"strategic_recommendations\"\", []))}'')\n    \n    # Video selection\n    video_selection = dashboard_data.get(''video_selection'', {})\n    selection_summary = video_selection.get(''selection_summary'', {})\n    print(f''\\n🎬 Analysis-Ready Videos:'')\n    print(f''  - Total Videos: {selection_summary.get(\"\"total_videos\"\", 0)}'')\n    print(f''  - High-Priority Candidates: {selection_summary.get(\"\"recommended_for_analysis\"\", 0)}'')\n    print(f''  - Average Analysis Potential: {selection_summary.get(\"\"average_analysis_potential\"\", 0)}%'')\n    \n    # Top video candidate\n    videos = video_selection.get(''videos'', [])\n    if videos:\n        top_video = videos[0]\n        potential = top_video.get(''analysis_readiness'', {}).get(''potential_score'', 0)\n        print(f''\\n🏆 TOP ANALYSIS CANDIDATE:'')\n        print(f''  Title: {top_video.get(\"\"title\"\", \"\"Unknown\"\")[:60]}...'')\n        print(f''  Analysis Potential: {potential}%'')\n        print(f''  Video ID: {top_video.get(\"\"video_id\"\", \"\"\"\")}'')\n    \n    # Save to accessible location\n    import shutil\n    result_dir = Path(''/Users/<USER>/Desktop/direrctor/enhanced-live-results'')\n    if result_dir.exists():\n        shutil.rmtree(result_dir)\n    shutil.copytree(artifacts_dir, result_dir)\n    \n    print(f''\\n📁 Enhanced results saved to:'')\n    print(f''   /Users/<USER>/Desktop/direrctor/enhanced-live-results/'')\n    print(f''\\n✅ COMPLETE ENHANCED WORKFLOW: SUCCESS'')\n    \nexcept Exception as e:\n    print(f''❌ Workflow failed: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "<PERSON><PERSON>(timeout:*)", "Bash(LIVE_PROVIDERS=false python -c \"\nimport tempfile\nimport json\nimport shutil\nfrom pathlib import Path\nfrom market_research_engine.pipeline.orchestrator import Orchestrator\nfrom market_research_engine.core.state import RunStatus, RunPhase\n\nprint(''=== CREATING TEST RUN WITH DASHBOARD ==='')\n\n# Create test run directory\ntest_dir = Path(''/Users/<USER>/Desktop/direrctor/market-research-engine/runs'')\nrun_id = ''dashboard-test-run''\nrun_dir = test_dir / run_id\nartifacts_dir = run_dir / ''artifacts''\n\n# Clean up if exists\nif run_dir.exists():\n    shutil.rmtree(run_dir)\n\nrun_dir.mkdir(parents=True)\nartifacts_dir.mkdir(parents=True)\n\n# Copy our enhanced dashboard data\nsource_path = Path(''/Users/<USER>/Desktop/direrctor/final-enhanced-results'')\nfor file in [''dashboard.json'', ''recon.json'', ''synthesis.json'', ''synthesis.md'', ''vision.json'']:\n    src = source_path / file\n    if src.exists():\n        shutil.copy2(src, artifacts_dir / file)\n        print(f''✅ Copied {file}'')\n\n# Create metadata\nmetadata = {\n    ''project'': ''dashboard-integration-test'',\n    ''params'': {''query'': ''content automation'', ''region'': ''US''},\n    ''metadata'': {},\n    ''created_at'': ''2025-08-06T12:00:00Z'',\n    ''run_id'': run_id,\n    ''run_dir'': str(run_dir),\n    ''app_env'': ''test''\n}\n\nwith open(run_dir / ''metadata.json'', ''w'') as f:\n    json.dump(metadata, f, indent=2)\n\n# Create status\nstatus_data = {\n    ''run_id'': run_id,\n    ''state'': ''succeeded'',\n    ''started_at'': ''2025-08-06T12:00:00Z'',\n    ''ended_at'': ''2025-08-06T12:05:00Z'',\n    ''version'': 1\n}\n\nwith open(run_dir / ''status.json'', ''w'') as f:\n    json.dump(status_data, f, indent=2)\n\nprint(f''📁 Test run created: {run_dir}'')\nprint(f''📊 Artifacts: {list(f.name for f in artifacts_dir.glob(\"\"*.json\"\") if f.is_file())}'')\n\")", "Bash(npm run dev:*)", "Bash(npm install:*)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)"], "deny": []}}