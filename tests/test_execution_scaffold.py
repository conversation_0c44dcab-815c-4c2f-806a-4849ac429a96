import asyncio
from market_research_engine.core.execution import (
    DefaultExecutor,
    MockExecutor,
    MockStep,
    ExecutionContext,
    ExecutionPolicy,
    RetryPolicy,
    TimeoutPolicy,
)


def test_sync_success_single_attempt():
    execu = DefaultExecutor()
    step = MockStep(step_id="sync-ok", fail_times=0, sleep_sec=0.0)
    ctx = ExecutionContext(run_id="run-sync-1")
    policy = ExecutionPolicy(retry=RetryPolicy(max_attempts=1), timeout=TimeoutPolicy(step_timeout_sec=None))

    res = execu.run_step(ctx, step, inputs={"x": 1}, policy=policy)
    assert res.succeeded is True
    assert res.cancelled is False
    assert res.timed_out is False
    assert res.attempts == 1
    assert res.duration_ms >= 0
    assert res.output_summary and res.output_summary.get("ok") is True


def test_sync_retries_then_success():
    execu = MockExecutor()
    step = MockStep(step_id="sync-retry", fail_times=2, sleep_sec=0.0)
    ctx = ExecutionContext(run_id="run-sync-retry")
    policy = ExecutionPolicy(retry=RetryPolicy(max_attempts=3), timeout=TimeoutPolicy(step_timeout_sec=None))

    res = execu.run_step(ctx, step, inputs={}, policy=policy)
    # Should succeed after 2 failures on attempt 3
    assert res.succeeded is True
    assert res.attempts == 3
    assert res.error_type is None


def test_sync_timeout():
    execu = DefaultExecutor()
    # sleep longer than timeout to trigger timeout error path
    step = MockStep(step_id="sync-timeout", fail_times=0, sleep_sec=0.2)
    ctx = ExecutionContext(run_id="run-sync-timeout")
    policy = ExecutionPolicy(retry=RetryPolicy(max_attempts=1), timeout=TimeoutPolicy(step_timeout_sec=0.05))

    res = execu.run_step(ctx, step, inputs={}, policy=policy)
    assert res.succeeded is False
    assert res.timed_out is True
    assert res.error_type in {"TimeoutError", "TimeoutError"}  # platform differences
    assert res.attempts == 1


def test_sync_cancellation_during_sleep():
    cancelled = False

    def is_cancelled():
        return True

    execu = DefaultExecutor()
    step = MockStep(step_id="sync-cancel", fail_times=0, sleep_sec=0.1)
    ctx = ExecutionContext(run_id="run-sync-cancel", is_cancelled=is_cancelled)
    policy = ExecutionPolicy(retry=RetryPolicy(max_attempts=1), timeout=TimeoutPolicy(step_timeout_sec=None))

    res = execu.run_step(ctx, step, inputs={}, policy=policy)
    assert res.succeeded is False
    assert res.cancelled is True
    assert res.error_type in {"RuntimeError", None}  # cancellation path raises RuntimeError("cancelled_during_sleep")


async def _async_run_success():
    execu = DefaultExecutor()
    step = MockStep(step_id="async-ok", fail_times=0, sleep_sec=0.0)
    ctx = ExecutionContext(run_id="run-async-ok", cancel_event=asyncio.Event())
    policy = ExecutionPolicy(retry=RetryPolicy(max_attempts=1), timeout=TimeoutPolicy(step_timeout_sec=None))
    return await execu.run_step_async(ctx, step, inputs={}, policy=policy)


def test_async_success_event_loop():
    res = asyncio.run(_async_run_success())
    assert res.succeeded is True
    assert res.cancelled is False
    assert res.timed_out is False
    assert res.attempts == 1


async def _async_run_timeout():
    execu = DefaultExecutor()
    step = MockStep(step_id="async-timeout", fail_times=0, sleep_sec=0.2)
    ctx = ExecutionContext(run_id="run-async-timeout", cancel_event=asyncio.Event())
    policy = ExecutionPolicy(retry=RetryPolicy(max_attempts=1), timeout=TimeoutPolicy(step_timeout_sec=0.05))
    return await execu.run_step_async(ctx, step, inputs={}, policy=policy)


def test_async_timeout_event_loop():
    res = asyncio.run(_async_run_timeout())
    assert res.succeeded is False
    assert res.timed_out is True
    assert res.attempts == 1