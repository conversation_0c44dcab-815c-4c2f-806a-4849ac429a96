<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Master SVG sprite containing 8 avatar tiles at 80x80 each. Exported PNGs will be provided per-tile in subsequent writes if required, but this SVG serves as a license-free source. -->
<svg width="656" height="176" viewBox="0 0 656 176" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Soft shadow -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%" color-interpolation-filters="sRGB">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.25"/>
    </filter>
    <!-- Clip to safe 64x64 circle inside 80x80 with 8px padding -->
    <clipPath id="circle64">
      <circle cx="40" cy="40" r="32"/>
    </clipPath>
    <!-- Inter fallback via system is handled at composition time; here we use vector paths only -->
  </defs>

  <!-- Avatar 01: Gradient teal/purple, initials AH -->
  <g transform="translate(8,8)" filter="url(#shadow)">
    <rect x="0" y="0" width="80" height="80" rx="16" fill="#0F0F17"/>
    <g clip-path="url(#circle64)">
      <rect x="0" y="0" width="80" height="80" fill="url(#g1)"/>
      <defs>
        <linearGradient id="g1" x1="0" y1="0" x2="80" y2="80" gradientUnits="userSpaceOnUse">
          <stop stop-color="#7F56D9"/>
          <stop offset="1" stop-color="#00D1B2"/>
        </linearGradient>
      </defs>
    </g>
    <circle cx="40" cy="40" r="32" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/>
    <text x="40" y="48" text-anchor="middle" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto" font-weight="700" font-size="24" fill="#E8E8F2">AH</text>
  </g>

  <!-- Avatar 02: Indigo/gold, initials BK -->
  <g transform="translate(92,8)" filter="url(#shadow)">
    <rect x="0" y="0" width="80" height="80" rx="16" fill="#0F0F17"/>
    <g clip-path="url(#circle64)">
      <rect x="0" y="0" width="80" height="80" fill="url(#g2)"/>
      <defs>
        <linearGradient id="g2" x1="0" y1="80" x2="80" y2="0" gradientUnits="userSpaceOnUse">
          <stop stop-color="#1E1E2A"/>
          <stop offset="1" stop-color="#E8C547"/>
        </linearGradient>
      </defs>
      <circle cx="16" cy="16" r="8" fill="rgba(232,197,71,0.32)"/>
      <circle cx="70" cy="64" r="12" fill="rgba(127,86,217,0.32)"/>
    </g>
    <circle cx="40" cy="40" r="32" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/>
    <text x="40" y="48" text-anchor="middle" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto" font-weight="700" font-size="24" fill="#E8E8F2">BK</text>
  </g>

  <!-- Avatar 03: Green/blue mesh, initials CP -->
  <g transform="translate(176,8)" filter="url(#shadow)">
    <rect x="0" y="0" width="80" height="80" rx="16" fill="#0F0F17"/>
    <g clip-path="url(#circle64)">
      <rect x="0" y="0" width="80" height="80" fill="#12121A"/>
      <path d="M-10,70 C20,40 60,100 90,60" stroke="#00C2FF" stroke-width="18" stroke-opacity="0.25"/>
      <path d="M-10,20 C20,50 60,-10 90,20" stroke="#039855" stroke-width="18" stroke-opacity="0.25"/>
    </g>
    <circle cx="40" cy="40" r="32" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/>
    <text x="40" y="48" text-anchor="middle" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto" font-weight="700" font-size="24" fill="#E8E8F2">CP</text>
  </g>

  <!-- Avatar 04: Orange/pink, initials DS -->
  <g transform="translate(260,8)" filter="url(#shadow)">
    <rect x="0" y="0" width="80" height="80" rx="16" fill="#0F0F17"/>
    <g clip-path="url(#circle64)">
      <rect x="0" y="0" width="80" height="80" fill="url(#g4)"/>
      <defs>
        <linearGradient id="g4" x1="0" y1="0" x2="80" y2="0" gradientUnits="userSpaceOnUse">
          <stop stop-color="#F79009"/>
          <stop offset="1" stop-color="#FF6B9E"/>
        </linearGradient>
      </defs>
      <rect x="-10" y="48" width="100" height="40" fill="rgba(247,144,9,0.16)"/>
    </g>
    <circle cx="40" cy="40" r="32" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/>
    <text x="40" y="48" text-anchor="middle" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto" font-weight="700" font-size="24" fill="#E8E8F2">DS</text>
  </g>

  <!-- Avatar 05: Mono neutral with dot motif, initials EL -->
    <g transform="translate(344,8)" filter="url(#shadow)">
      <rect x="0" y="0" width="80" height="80" rx="16" fill="#0F0F17"/>
      <g clip-path="url(#circle64)">
        <rect x="0" y="0" width="80" height="80" fill="#161625"/>
        <g fill="#B8B8C9" fill-opacity="0.18">
          <circle cx="12" cy="12" r="3"/>
          <circle cx="44" cy="8" r="2.5"/>
          <circle cx="70" cy="22" r="2.5"/>
          <circle cx="26" cy="34" r="2.5"/>
          <circle cx="60" cy="60" r="3"/>
          <circle cx="16" cy="64" r="2.5"/>
        </g>
      </g>
      <circle cx="40" cy="40" r="32" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/>
      <text x="40" y="48" text-anchor="middle" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto" font-weight="700" font-size="24" fill="#E8E8F2">EL</text>
    </g>

  <!-- Avatar 06: Emerald, initials FM -->
  <g transform="translate(428,8)" filter="url(#shadow)">
    <rect x="0" y="0" width="80" height="80" rx="16" fill="#0F0F17"/>
    <g clip-path="url(#circle64)">
      <rect x="0" y="0" width="80" height="80" fill="#0F1A14"/>
      <circle cx="40" cy="40" r="40" fill="url(#g6)"/>
      <defs>
        <radialGradient id="g6" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(20 20) rotate(45) scale(60)">
          <stop offset="0" stop-color="#039855" stop-opacity="0.9"/>
          <stop offset="1" stop-color="#039855" stop-opacity="0.2"/>
        </radialGradient>
      </defs>
    </g>
    <circle cx="40" cy="40" r="32" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/>
    <text x="40" y="48" text-anchor="middle" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto" font-weight="700" font-size="24" fill="#E8E8F2">FM</text>
  </g>

  <!-- Avatar 07: Indigo grid, initials GN -->
  <g transform="translate(512,8)" filter="url(#shadow)">
    <rect x="0" y="0" width="80" height="80" rx="16" fill="#0F0F17"/>
    <g clip-path="url(#circle64)">
      <rect x="0" y="0" width="80" height="80" fill="#17172A"/>
      <path d="M0 20H80M0 40H80M0 60H80M20 0V80M40 0V80M60 0V80" stroke="rgba(127,86,217,0.25)" stroke-width="1"/>
    </g>
    <circle cx="40" cy="40" r="32" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/>
    <text x="40" y="48" text-anchor="middle" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto" font-weight="700" font-size="24" fill="#E8E8F2">GN</text>
  </g>

  <!-- Avatar 08: Warm gradient, initials HO -->
  <g transform="translate(596,8)" filter="url(#shadow)">
    <rect x="0" y="0" width="80" height="80" rx="16" fill="#0F0F17"/>
    <g clip-path="url(#circle64)">
      <rect x="0" y="0" width="80" height="80" fill="url(#g8)"/>
      <defs>
        <linearGradient id="g8" x1="0" y1="80" x2="80" y2="0" gradientUnits="userSpaceOnUse">
          <stop stop-color="#F79009"/>
          <stop offset="1" stop-color="#E53E3E"/>
        </linearGradient>
      </defs>
      <path d="M-10 60 C20 20 60 100 90 40" stroke="rgba(255,255,255,0.24)" stroke-width="14"/>
    </g>
    <circle cx="40" cy="40" r="32" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/>
    <text x="40" y="48" text-anchor="middle" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto" font-weight="700" font-size="24" fill="#E8E8F2">HO</text>
  </g>

  <!-- Row 2 mirrors (optional spacing for sheet completeness) -->
  <!-- Empty padding row -->
  <rect x="0" y="96" width="656" height="72" fill="none"/>
</svg>